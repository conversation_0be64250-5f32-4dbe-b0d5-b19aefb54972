import { Skeleton } from "@/components/ui/skeleton"

export default function ProductDetailSkeleton() {
  return (
    <div className="container mx-auto px-4 py-8 max-w-3xl">
      <Skeleton className="h-8 w-32 mb-6" />

      <div className="flex flex-col md:flex-row gap-8 mb-8">
        {/* Image skeleton */}
        <div className="w-full md:w-1/2">
          <Skeleton className="aspect-square w-full rounded-lg" />
        </div>

        {/* Product details skeleton */}
        <div className="w-full md:w-1/2 space-y-4">
          <Skeleton className="h-10 w-3/4" />

          <div className="flex items-center space-x-2">
            {[...Array(5)].map((_, i) => (
              <Skeleton key={i} className="w-5 h-5 rounded-full" />
            ))}
            <Skeleton className="h-4 w-8 ml-2" />
          </div>

          <Skeleton className="h-6 w-24 ml-auto" />

          <div className="space-y-4">
            <Skeleton className="h-12 w-full rounded-full" />
            <Skeleton className="h-12 w-full rounded-full" />
          </div>
        </div>
      </div>

      {/* Collapsible sections skeleton */}
      {[...Array(5)].map((_, i) => (
        <div key={i} className="border-t border-gray-200 py-6">
          <div className="flex justify-between">
            <Skeleton className="h-6 w-32" />
            <Skeleton className="h-6 w-6 rounded-full" />
          </div>
          <div className="mt-4">
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-full mb-2" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>
      ))}
    </div>
  )
}
