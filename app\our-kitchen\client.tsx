"use client";
import "../styles/our-kitchen.css";
import NewsletterSignup from "@/components/newsletter-signup";
import { useState } from "react";
import { useAuth } from "@/context/auth-context";
import AuthModalWrapper from "@/components/auth-modal-wrapper";

export default function OurKitchenClient() {
  const [isExpanded, setIsExpanded] = useState(false);
  const [authModalOpen, setAuthModalOpen] = useState(false);
  const [authModalView, setAuthModalView] = useState<
    "login" | "signup" | "forgot-password"
  >("signup");
  const { isAuthenticated } = useAuth();

  return (
    <main className="">
      <section className="static_banner_section">
        <div className="static_banner_arrows">
          <div className="static_bannerSlider_arrow_box"></div>
        </div>
        <div className="static_Banner_slider">
          <div className="static_Banner">
            <figure>
              <img
                src="https://static.tossdown.com/images/cdd8b8f9-c55d-44c5-b18e-0ebc6e484258.webp"
                alt="Banner desktop"
                className="banner_desk_img"
              />
              <img
                src="https://static.tossdown.com/images/cdd8b8f9-c55d-44c5-b18e-0ebc6e484258.webp"
                alt="Banner mobile"
                className="banner_mobile_img"
              />
            </figure>
          </div>
        </div>
      </section>
      <div className="working_process_parent">
        <div className="working_process_parent">
          <div className="working_process_heading">
            <div className="process_heading_parent">
              <figure>
                <img
                  className="custom_image_sec"
                  src="https://static.tossdown.com/site/c8c38c9b-4193-47f4-ae88-4615fb5f01a9.webp"
                  alt="Kitchen icon"
                />
              </figure>
              <div className="process_heading_custom">
                <h2>welcome to OUR KITCHEN</h2>

                <small>where delicious memories are made!</small>
              </div>
            </div>
          </div>
          <div className="working_process_main">
            <div className="custom_11">
              <figure className="custom_11_fig">
                <img
                  className="custom_11_ab_1"
                  src="https://static.tossdown.com/images/03dd9adf-abe7-4b33-93ab-000fca7b0eaf.webp"
                  alt="About us"
                />
              </figure>
              <div className="custom_11_body_main">
                <div className="custom_11_body">
                  <div className="custom_11_heading first_sec_heading">
                    <figure className="ab_one">
                      <img
                        src="https://static.tossdown.com/site/f385ea8a-99ac-46bb-9ed9-38e7c4152c39.webp"
                        alt="About icon"
                      />
                    </figure>
                    <div>
                      <div className="heading_box">
                        <h3>ABOUT US</h3>
                      </div>
                      {(() => {
                        const firstParagraph =
                          "EZeats is a convenient, delicious and affordable online meal solution for busy lives! Our platform offers a wide variety of delicious and flavorful meal options, carefully crafted by our team of experienced chefs and professionals. Our meals are made with fresh, locally-sourced ingredients and are designed to be quick and easy to prepare.";

                        const secondParagraph =
                          "At EZeats, we are committed to delivering the highest quality food, prepared with the utmost care and attention to detail. Our meals are crafted in a state-of-the-art facility that meets the stringent standards of food safety, SQF certified, and globally recognized by GFSI. This ensures that every dish we serve is not only of the highest quality but also of the highest food safety. We take pride in using only Halal-certified meat & ingredients, ensuring that our dishes adhere to strictest halal guidelines and HMA certification. ";

                        const truncatedSecondParagraph =
                          secondParagraph.substring(0, 100) + "... ";

                        return (
                          <p>
                            {firstParagraph}
                            <br />
                            <br />
                            {isExpanded
                              ? secondParagraph
                              : truncatedSecondParagraph}
                            <button
                              onClick={() => setIsExpanded(!isExpanded)}
                              className="text-[#FFCC00] font-medium hover:underline focus:outline-none text-sm"
                            >
                              {isExpanded ? "Read less" : "Read more"}
                            </button>
                          </p>
                        );
                      })()}
                    </div>
                    <figure className="responsive_one">
                      <img
                        src="https://static.tossdown.com/site/c8c38c9b-4193-47f4-ae88-4615fb5f01a9.webp"
                        alt="About icon responsive"
                      />
                    </figure>
                  </div>
                </div>
              </div>
            </div>
            <div className="custom_13_parent">
              <div className="custom_13">
                <div className="custom_13_content">
                  <h2>YOUR BENEFITS</h2>
                  <ul>
                    <li>
                      Whether you're a busy professional, a student, or a family
                      on-the-go, Ezeats has got you covered with our easy online
                      ordering and fast delivery service.
                    </li>
                    <li>
                      Whether you're in the mood for spicy Indian dishes, rich
                      Italian pasta, or some Asian flavors, EZeats has got them
                      all.
                    </li>
                    <li>
                      Enjoy our customizable and flexible subscription plans or
                      use our One-time ordering system that fits your schedule
                      and budget.
                    </li>
                    <li>
                      With prices starting at just $5.99 per serving, you can
                      enjoy restaurant-quality and budget-friendly meals,
                      without the hassle of cooking or cleaning up.
                    </li>
                  </ul>
                </div>
              </div>
              <figure className="custom_13_dek_img">
                <img
                  src="https://static.tossdown.com/images/777332ca-658b-4c70-806e-b8e2c54ea9b7.webp"
                  alt="Benefits desktop"
                />
              </figure>
              <figure className="custom_13_mob_img">
                <img
                  src="https://static.tossdown.com/images/b2e9cd4b-b8ad-41b3-baae-f65bce2a9601.webp"
                  alt="Benefits mobile"
                />
              </figure>
            </div>
            <div className="custom_12">
              <img
                className="custom_12_ab_1"
                src="https://static.tossdown.com/images/0b2c1b68-0fc1-4bd3-a98d-7ccbd29ff89b.webp"
                alt="Goal decoration"
              />
              <div className="custom_12_body">
                <div className="custom_12_heading">
                  <h3>Our GOAL</h3>
                  <p>
                    Our goal is to provide affordable, convenient, and delicious
                    meals that fit your busy lifestyle. We believe that everyone
                    deserves to enjoy a good meal, regardless of their budget or
                    cooking skills. We strive to make mealtime a stress-free and
                    enjoyable experience without compromising on flavor or
                    breaking the bank.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <section className="custom_sec_14">
        <div className="custom_container">
          <div className="custom_14_data_parent">
            <div className="custom_14_data_1">
              <strong>Say Goodbye</strong> <span>to meal prep stress AND</span>
            </div>
            <div className="custom_14_data_2">
              <strong>HELLO</strong>{" "}
              <span>
                to more free time with ez<small>eats!</small>
              </span>
            </div>
            <p>THE PERFECT SOLUTION FOR A QUICK, EASY, AND AFFORDABLE MEAL!</p>
          </div>

          <div className="custom_14_data_parent_2">
            <strong>
              join <small>us!</small>
            </strong>{" "}
            <span></span>
            <p>
              Sign up today and discover the convenience of EZeats, your
              one-stop-shop for all your food cravings! With a diverse range of
              meals to choose from, you can indulge in your favorite dishes from
              the comfort of your home. With just a few clicks, you can order
              mouth-watering meals that cater to your needs and preferences in
              just a few hours.
            </p>
          </div>
        </div>
      </section>

      {/* Sign Up Now button - only shown when user is not authenticated */}
      {!isAuthenticated && (
        <div className="signup_btn mb-[60px]">
          <a
            href="#"
            id="signupClick"
            onClick={(e) => {
              e.preventDefault();
              // Show the signup modal
              setAuthModalView("signup");
              setAuthModalOpen(true);
            }}
          >
            Sign Up Now
          </a>
        </div>
      )}

      {/* Add the NewsletterSignup component */}
      <NewsletterSignup />

      {/* Auth Modal */}
      <AuthModalWrapper
        isOpen={authModalOpen}
        initialView={authModalView}
        onClose={() => setAuthModalOpen(false)}
      />
    </main>
  );
}
