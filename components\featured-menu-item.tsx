"use client";

import Image from "next/image";
import { useProductModal } from "@/context/product-modal-context";
import { useRouter } from "next/navigation";
import StarRating from "./star-rating";

type FeaturedMenuItemProps = {
  id: number;
  name: string;
  image: string;
  rating: number;
  price: number;
  isVeg: boolean;
  inStock?: boolean;
};

export default function FeaturedMenuItem({
  id,
  name,
  image,
  rating,
  price,
  isVeg,
  inStock = true,
}: FeaturedMenuItemProps) {
  const { openProductModal } = useProductModal();
  const router = useRouter();

  // Update the handleClick function to navigate to the product page with the slug
  const handleClick = () => {
    // You can either open the modal or navigate to the product page
    // For direct navigation to the product page:
    // router.push(`/product/${generateProductSlug(name, id)}`)

    // Or keep using the modal:
    openProductModal(id);
  };

  return (
    <div onClick={handleClick} className="block cursor-pointer">
      <div className="flex flex-col items-center w-full single_card_box">
        {/* 1. Image - Perfectly square with consistent dimensions */}
        <div className="w-full aspect-square relative mb-3 overflow-hidden ">
          <Image
            src={image || "/placeholder.svg"}
            alt={name}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 280px"
          />
          <div className="absolute top-2 left-2 bg-black text-white text-xs px-2 py-1 rounded-sm">
            {isVeg ? "Veg" : "Non Veg"}
          </div>
        </div>

        {/* For featured items, we don't show quantity selector but keep the same sequence */}
        {/* OUT OF STOCK indicator */}
        {/*   {!inStock && <div className="d-none  text-red-600 font-medium text-sm mb-3">OUT OF STOCK</div>} */}

        {/* 3. Name */}
        <h3 className="text-center font-bold text-lg mb-2 pro_name">{name}</h3>

        {/* 4. Star ratings */}
        <div className="flex justify-center mb-2 product_rating_parent">
          <StarRating rating={rating} starSize={20} fillColor="#f97316" />
        </div>

        {/* 5. Price */}
        <div className="card_price_box">
          <p className="text-center font-medium ">CAD {price.toFixed(2)}</p>
        </div>
      </div>
    </div>
  );
}
