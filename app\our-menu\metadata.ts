import type { Metadata } from "next"
import { getPageSeo } from "@/services/seo-service"

export async function generateMetadata(): Promise<Metadata> {
  // Use "menu" XML page name for the our-menu page
  const pageSeo = await getPageSeo("menu")

  if (!pageSeo) {
    return {
      title: "Our Menu - EZeats Canada",
    }
  }

  // Create metadata object with only the values from XML
  const metadata: Metadata = {
    title: pageSeo.seo.pagetitle || "Our Menu - EZeats Canada",
  }

  // Only add description if it exists in the XML
  if (pageSeo.seo.desc) {
    metadata.description = pageSeo.seo.desc
  }

  // Only add keywords if they exist in the XML
  if (pageSeo.seo.keywords) {
    metadata.keywords = pageSeo.seo.keywords
  }

  return metadata
}
