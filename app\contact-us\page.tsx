import type { Metada<PERSON> } from "next";
import { generatePageMetadata } from "@/lib/metadata-utils";
import { getPageH1 } from "@/lib/seo-utils";
import ContactUsClient from "./ContactUsClient";

export async function generateMetadata(): Promise<Metadata> {
  // Use "contact" XML page name for the contact-us page
  return generatePageMetadata("contact");
}

export default async function ContactUsPage() {
  // Get the H1 from XML
  const h1 = await getPageH1("contact");

  return (
    <>
      {/* Use H1 from XML if available */}
      {h1 && <h1 className="sr-only">{h1}</h1>}
      <ContactUsClient />
    </>
  );
}
