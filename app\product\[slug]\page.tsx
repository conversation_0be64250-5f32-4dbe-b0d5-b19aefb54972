import type { Metada<PERSON> } from "next"
import { generateProductMetadata } from "@/lib/metadata-utils"
import { fetchProductDetail, mapProductDetailToViewModel } from "@/services/product-detail-api"
import ProductDetailClient from "./client"
import { getProductSeo } from "@/services/seo-api"
import { extractProductIdFromSlug } from "@/utils/product-utils"

export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  try {
    // Extract product ID from the slug
    const productId = extractProductIdFromSlug(params.slug)

    // Fetch product details to get the product name
    const productDetails = await fetchProductDetail(productId)

    if (productDetails && productDetails.items && productDetails.items.length > 0) {
      const mappedProduct = mapProductDetailToViewModel(productDetails)
      if (mappedProduct) {
        // Use the product name for SEO
        return generateProductMetadata(mappedProduct.name)
      }
    }
  } catch (error) {
    console.error("Error generating product metadata:", error)
  }

  // Fallback metadata
  return {
    title: "Product Details - EZeats",
  }
}

export default async function ProductDetailPage({ params }: { params: { slug: string } }) {
  let product = null
  let error = null
  let h1 = null

  try {
    // Extract product ID from the slug
    const productId = extractProductIdFromSlug(params.slug)

    const productDetails = await fetchProductDetail(productId)

    if (productDetails && productDetails.items && productDetails.items.length > 0) {
      product = mapProductDetailToViewModel(productDetails)

      // Get the SEO data for this product
      if (product) {
        const seoData = await getProductSeo(product.name)
        h1 = seoData?.h1 || null
      }
    } else {
      error = "Product not found"
    }
  } catch (err) {
    console.error("Error loading product:", err)
    error = "Failed to load product details"
  }

  return (
    <>
      {/* Use H1 from XML if available */}
      {h1 && <h1 className="sr-only">{h1}</h1>}
      <ProductDetailClient product={product} error={error} productId={params.slug} />
    </>
  )
}
