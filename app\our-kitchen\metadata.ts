import type { Metadata } from "next"
import { getPageSeo } from "@/services/seo-service"

export async function generateMetadata(): Promise<Metadata> {
  // Note: In the XML, this page is named "about"
  const pageSeo = await getPageSeo("about")

  return {
    title: pageSeo?.seo?.pagetitle || "Our Kitchen - EZeats Canada",
    description: pageSeo?.seo?.desc || "Learn about our kitchen and how we prepare fresh, delicious meals.",
    keywords: pageSeo?.seo?.keywords || "about EZeats, our kitchen, meal preparation",
  }
}
