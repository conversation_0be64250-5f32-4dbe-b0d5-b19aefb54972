"use client"

import { createContext, useContext, useState, type ReactNode } from "react"
import { useRouter } from "next/navigation"
import { generateProductSlug } from "@/utils/product-utils"

type ProductModalContextType = {
  isModalOpen: boolean
  selectedProductId: number | null
  openProductModal: (productId: number) => void
  closeProductModal: () => void
  navigateToProductPage: (id: number, name: string) => void
}

const ProductModalContext = createContext<ProductModalContextType | undefined>(undefined)

export function ProductModalProvider({ children }: { children: ReactNode }) {
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedProductId, setSelectedProductId] = useState<number | null>(null)
  const router = useRouter()

  const openProductModal = (productId: number) => {
    setSelectedProductId(productId)
    setIsModalOpen(true)
    // Prevent body scrolling when modal is open
    document.body.style.overflow = "hidden"
  }

  const closeProductModal = () => {
    setIsModalOpen(false)
    // Re-enable body scrolling when modal is closed
    document.body.style.overflow = "auto"
  }

  const navigateToProductPage = (id: number, name: string) => {
    router.push(`/product/${generateProductSlug(name, id)}`)
  }

  return (
    <ProductModalContext.Provider
      value={{
        isModalOpen,
        selectedProductId,
        openProductModal,
        closeProductModal,
        navigateToProductPage,
      }}
    >
      {children}
    </ProductModalContext.Provider>
  )
}

export function useProductModal() {
  const context = useContext(ProductModalContext)
  if (context === undefined) {
    throw new Error("useProductModal must be used within a ProductModalProvider")
  }
  return context
}
