import { getPageSeo } from "@/services/seo-service"

/**
 * Gets the H1 tag content for a specific page
 */
export async function getPageH1(pageName: string): Promise<string | null> {
  try {
    const pageSeo = await getPageSeo(pageName)

    if (!pageSeo || !pageSeo.seo.h1) {
      return null
    }

    return pageSeo.seo.h1
  } catch (error) {
    console.error(`Error getting H1 for page ${pageName}:`, error)
    return null
  }
}
