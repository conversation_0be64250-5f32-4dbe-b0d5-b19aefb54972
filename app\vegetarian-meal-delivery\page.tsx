import type { Metadata } from "next";
import VegetarianMealClient from "./client";

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Fresh Vegetarian Meal Delivery in Mississauga | EZeats",
    description:
      "Here's the most affordable meal delivery service that provides fresh, vegetarian meals and tiffin service for delivery in just an hour!",
    keywords:
      "meal delivery service, meal delivery services, vegetarian meals, tiffin service, indian food, meal kit,vegetables, chana masala, aloo gobi, palak paneer, shahi paneer, bhindi masala, Vegetable biryani, homemade meals, Mississauga, Etobicoke, Brampton",
  };
}

export default async function VegetarianMealPage() {
  // Try to get the H1 from XML, or use a default
  const h1 = "Delicious Vegetarian Meal Delivery Service";

  return (
    <>
      {/* Use H1 from XML if available */}
      {h1 && <h1 className="sr-only">{h1}</h1>}
      <VegetarianMealClient initialH1={h1} />
    </>
  );
}
