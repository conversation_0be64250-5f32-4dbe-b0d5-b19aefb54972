import type React from "react";
import type { Metadata } from "next";
import ClientLayout from "./ClientLayout";
import "./globals.css";
import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>_<PERSON>eue } from "next/font/google";

// Load Anton font for headings
const anton = Anton({
  weight: ["400"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-anton",
});

// Load Poppins font for body text
const poppins = Poppins({
  weight: ["300", "400", "500", "600", "700"],
  subsets: ["latin"],
  display: "swap",
  variable: "--font-poppins",
});

const bebasNeue = Bebas_Neue({
  weight: ["400"],
  subsets: ["latin"],
  variable: "--font-bebas-neue",
});

export const metadata: Metadata = {
  title: "EZeats Canada - Tiffin Service and Food Delivery",
  description: "",
  generator: "v0.dev",
  icons: {
    icon: "/favicon.ico",
  },
  viewport: "width=device-width, initial-scale=1, maximum-scale=1",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html
      lang="en"
      className={`${poppins.variable} ${anton.variable} ${bebasNeue.variable}`}
    >
      <head>
        {/* Initialize dataLayer */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.dataLayer = window.dataLayer || [];
              window.dataLayer.push({
                'gtm.start': new Date().getTime(),
                event: 'gtm.js'
              });
            `,
          }}
        />
        {/* Google Tag Manager */}
        <script
          dangerouslySetInnerHTML={{
            __html: `(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer','GTM-MC65SQKK');`,
          }}
        />
        {/* End Google Tag Manager */}

        {/* Google site verification */}
        <meta
          name="google-site-verification"
          content="hSrWiaBv_VUmcqPe2X5B-camgqGO74snFHSxVcAGSJA"
        />
        {/* Add index for development domain */}
        <meta name="robots" content="index, follow" />
        {/* Product description CSS for the modal */}
        <link rel="stylesheet" href="/styles/product-description.css" />
        <link
          rel="icon"
          href="https://static.tossdown.com/logos/7e6db3d0-6ab3-4a79-9e1f-2fc36405e81d.webp"
          type="image/webp"
        />
        {/* Add Slick Carousel CSS */}
        <link
          rel="stylesheet"
          type="text/css"
          charSet="UTF-8"
          href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick.min.css"
        />
        <link
          rel="stylesheet"
          type="text/css"
          href="https://cdnjs.cloudflare.com/ajax/libs/slick-carousel/1.8.1/slick-theme.min.css"
        />
        <link
          rel="stylesheet"
          href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"
          integrity="sha512-iecdLmaskl7CVkqkXNQ/ZH/XLlvWZOJyj7Yy7tcenmpD1ypASozpmT/E0iPtmFIB46ZmdtAc9eNBvH0H/ZpiBw=="
          crossOrigin="anonymous"
          referrerPolicy="no-referrer"
        />
        {/* Fixed script to safely check for document before accessing classList */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
            (function() {
              // Only run in browser environment
              if (typeof window !== 'undefined' && typeof document !== 'undefined' && document.body) {
                // Add no-fouc class to body
                document.body.classList.add('no-fouc');

                // Remove the class when styles are loaded
                window.addEventListener('load', function() {
                  if (document.body) {
                    document.body.classList.remove('no-fouc');
                  }
                });


              }
            })();
          `,
          }}
        />
      </head>
      <body className="font-poppins copyright_footer">
        {/* Google Tag Manager (noscript) */}
        <noscript>
          <iframe
            src="https://www.googletagmanager.com/ns.html?id=GTM-MC65SQKK"
            height="0"
            width="0"
            style={{ display: "none", visibility: "hidden" }}
            title="Google Tag Manager"
          ></iframe>
        </noscript>
        {/* End Google Tag Manager (noscript) */}
        <ClientLayout>{children}</ClientLayout>
      </body>
    </html>
  );
}
