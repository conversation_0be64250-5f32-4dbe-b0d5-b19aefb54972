# EZeats - Meal Delivery Platform

EZeats is a modern meal delivery platform that allows users to order pre-prepared meals in boxes of 6 or 12. The platform supports both delivery and pickup options, with features for scheduling deliveries, managing subscriptions, and more.

![EZeats Screenshot](/placeholder.svg?height=400&width=800&query=EZeats%20meal%20delivery%20platform%20screenshot)

## Features

- 🍽️ Meal box selection (6 or 12 meals)
- 🚚 Delivery and pickup options
- 📅 Scheduling for later, weekly, or bi-weekly deliveries
- 👤 User authentication and profile management
- 🛒 Cart management with real-time updates
- 📱 Responsive design for all devices
- 🗺️ Google Maps integration for address selection and validation
- 💳 Checkout process with order summary
- 📊 Order history and activity tracking

## Tech Stack

- **Frontend**: Next.js 14, React 18, TypeScript
- **Styling**: Tailwind CSS, shadcn/ui components
- **State Management**: React Context API
- **Authentication**: Custom auth with cookies
- **Maps**: Google Maps API
- **Deployment**: Vercel

## Prerequisites

Before you begin, ensure you have the following installed:
- Node.js (v18 or later)
- npm or yarn
- Git

## Getting Started

### Clone the Repository

\`\`\`bash
git clone https://github.com/yourusername/ezeats.git
cd ezeats
\`\`\`

### Install Dependencies

\`\`\`bash
npm install
# or
yarn install
\`\`\`

### Environment Variables

Create a `.env.local` file in the root directory with the following variables:

\`\`\`
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=your_google_maps_api_key
SENDGRID_API_KEY=your_sendgrid_api_key
NEXT_PUBLIC_BUSINESS_ID=12536
\`\`\`

> **Note**: You need a valid Google Maps API key with the Places API enabled for address autocomplete functionality.

### Running the Development Server

\`\`\`bash
npm run dev
# or
yarn dev
\`\`\`

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

## Project Structure

\`\`\`
├── app/                  # Next.js App Router pages
│   ├── api/              # API routes
│   ├── checkout/         # Checkout page
│   ├── contact-us/       # Contact us page
│   ├── menu/             # Menu page (for authenticated users)
│   ├── our-kitchen/      # Our kitchen page
│   ├── our-menu/         # Public menu page
│   ├── profile/          # User profile pages
│   ├── layout.tsx        # Root layout
│   └── page.tsx          # Home page
├── components/           # React components
│   ├── icons/            # SVG icons as React components
│   └── ui/               # UI components (shadcn)
├── context/              # React context providers
├── hooks/                # Custom React hooks
├── lib/                  # Utility functions
├── public/               # Static assets
├── services/             # API service functions
├── styles/               # CSS styles
├── types/                # TypeScript type definitions
└── utils/                # Utility functions
\`\`\`

## Key Components

- **Cart Context**: Manages the shopping cart state and operations
- **Auth Context**: Handles user authentication and session management
- **Business Context**: Provides business information like locations and settings
- **Product Modal**: Displays detailed product information
- **Address Autocomplete**: Google Maps integration for address selection

## Environment Variables

| Variable | Description |
|----------|-------------|
| `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY` | Google Maps API key for address autocomplete |
| `SENDGRID_API_KEY` | SendGrid API key for email notifications |
| `NEXT_PUBLIC_BUSINESS_ID` | Business ID for API requests |

## API Integration

The application integrates with several backend APIs:
- Product catalog API
- Cart management API
- User authentication API
- Address validation API
- Order management API

## Contributing

1. Fork the repository
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Acknowledgments

- Design inspiration from various meal delivery services
- Icons from Lucide React
- UI components from shadcn/ui
