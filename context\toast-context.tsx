"use client";

import type React from "react";
import {
  createContext,
  useContext,
  useState,
  useCallback,
  useRef,
} from "react";

// Define the Toast component directly in the context file to avoid circular dependencies
function Toast({
  message,
  type = "info",
  onClose,
}: {
  message: string;
  type?: "success" | "error" | "warning" | "info";
  onClose: () => void;
}) {
  const getToastClasses = () => {
    const baseClasses =
      "fixed left-1/2 transform -translate-x-1/2 z-[12000] rounded-md shadow-lg";

    switch (type) {
      case "success":
        return `${baseClasses} bottom-4 bg-green-500 text-white p-4 flex items-center justify-between`;
      case "error":
        return `${baseClasses} top-1/2 -translate-y-1/2 w-[290px] bg-[#f5c6cb] text-[#721c24] font-medium text-sm leading-4 p-[14px_20px] rounded-[25px] shadow-lg flex flex-col items-center`;
      case "warning":
        return `${baseClasses} bottom-4 bg-yellow-500 text-white p-4 flex items-center justify-between`;
      default:
        return `${baseClasses} bottom-4 bg-blue-500 text-white p-4 flex items-center justify-between`;
    }
  };

  return (
    <div className={getToastClasses()}>
      {type === "error" ? (
        <>
          <span className="mb-3 text-center">{message}</span>
          <button
            onClick={onClose}
            className="bg-[#721c24] text-white px-4 py-1 rounded-full text-xs font-medium"
          >
            OK
          </button>
        </>
      ) : (
        <>
          <span className="mr-4">{message}</span>
          <button
            onClick={onClose}
            className={type === "error" ? "text-[#721c24]" : "text-white"}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </>
      )}
    </div>
  );
}

export type ToastProps = {
  message: string;
  type?: "success" | "error" | "warning" | "info";
  duration?: number;
  onClose?: () => void;
};

type ToastContextType = {
  showToast: (props: Omit<ToastProps, "onClose">) => void;
};

const ToastContext = createContext<ToastContextType | undefined>(undefined);

export function ToastProvider({ children }: { children: React.ReactNode }) {
  const [toast, setToast] = useState<ToastProps | null>(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  const showToast = useCallback((props: Omit<ToastProps, "onClose">) => {
    // Clear any existing timeout
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }

    // Set the new toast
    setToast({ ...props, onClose: () => setToast(null) });

    // Auto-close after duration
    if (props.duration) {
      timeoutRef.current = setTimeout(() => {
        setToast(null);
      }, props.duration);
    }
  }, []);

  const handleClose = useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    setToast(null);
  }, []);

  return (
    <ToastContext.Provider value={{ showToast }}>
      {children}
      {toast && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={handleClose}
        />
      )}
    </ToastContext.Provider>
  );
}

export function useToast() {
  const context = useContext(ToastContext);
  if (context === undefined) {
    throw new Error("useToast must be used within a ToastProvider");
  }
  return context;
}
