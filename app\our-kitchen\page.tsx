import type { Metada<PERSON> } from "next"
import { generatePageMetadata } from "@/lib/metadata-utils"
import { getPageH1 } from "@/lib/seo-utils"
import OurKitchenClient from "./client"

export async function generateMetadata(): Promise<Metadata> {
  // Use "about" XML page name for the our-kitchen page
  return generatePageMetadata("about")
}

export default async function OurKitchenPage() {
  // Get the H1 from XML
  const h1 = await getPageH1("about")

  return (
    <>
      {/* Use H1 from XML if available */}
      {h1 && <h1 className="sr-only">{h1}</h1>}
      <OurKitchenClient />
    </>
  )
}
