// services/seo-api.tsx

import { fetchSeoData, type SeoData } from "@/services/seo-service"

/**
 * Gets SEO data for a product detail page, replacing placeholders
 */
export async function getProductSeo(productName: string): Promise<SeoData | null> {
  const seoData = await fetchSeoData()
  const detailPage = seoData["detail"]

  if (!detailPage) {
    console.warn("No SEO data found for product detail page")
    return null
  }

  // Clone the SEO data
  const productSeo: SeoData = {}

  // Only include fields that exist in the XML and have content
  if (detailPage.seo.pagetitle) {
    productSeo.pagetitle = detailPage.seo.pagetitle.replace(/<product_name>/g, productName)
  }

  if (detailPage.seo.h1) {
    productSeo.h1 = detailPage.seo.h1.replace(/<product_name>/g, productName)
  }

  if (detailPage.seo.desc) {
    productSeo.desc = detailPage.seo.desc.replace(/<product_name>/g, productName)
  }

  if (detailPage.seo.keywords) {
    productSeo.keywords = detailPage.seo.keywords
  }

  return productSeo
}
