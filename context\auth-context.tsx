"use client"

import { create<PERSON>ontext, use<PERSON>ontext, useState, useEffect, type ReactNode } from "react"
import { getCookies, clearUserAuth, type UserAuthDetails } from "@/utils/auth-utils"

// Update the User type to match our UserAuthDetails
type User = UserAuthDetails & {
  id: string
}

type AuthContextType = {
  user: User | null
  isAuthenticated: boolean
  logout: () => void
  refreshAuthState: () => void
}

// Create a default context value to prevent undefined errors
const defaultContextValue: AuthContextType = {
  user: null,
  isAuthenticated: false,
  logout: () => {},
  refreshAuthState: () => false,
}

const AuthContext = createContext<AuthContextType>(defaultContextValue)

export function AuthProvider({ children }: { children: ReactNode }) {
  const [user, setUser] = useState<User | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  // Function to check auth state from cookies
  const refreshAuthState = () => {
    try {
      // Check if we're in a browser environment
      if (typeof window === "undefined") {
        return false
      }

      const userCookieData = getCookies()

      // Only authenticate if userCookieData exists AND has a userId field
      if (userCookieData && userCookieData.userId) {
        try {
          // Map cookie data to our User type
          const userData: User = {
            ...userCookieData,
            id: userCookieData.userId,
          }
          setUser(userData)
          return true
        } catch (error) {
          console.error("Error parsing user data:", error)
          clearUserAuth() // Clear invalid data
          setUser(null)
          return false
        }
      } else {
        // If no userCookieData or no userId, user is not authenticated
        setUser(null)
        return false
      }
    } catch (error) {
      console.error("Error in refreshAuthState:", error)
      setUser(null)
      return false
    }
  }

  // Initialize auth state on mount
  useEffect(() => {
    try {
      refreshAuthState()
    } catch (error) {
      console.error("Error initializing auth state:", error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Update the logout function to use proper Next.js patterns instead of page reload
  const logout = () => {
    try {
      // Clear cookies first
      clearUserAuth()

      // Update state
      setUser(null)
    } catch (error) {
      console.error("Error during logout:", error)
    }
  }

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user && !!user.id, // Ensure both user and user.id exist
        logout,
        refreshAuthState,
      }}
    >
      {!isLoading ? children : null}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  return useContext(AuthContext)
}
