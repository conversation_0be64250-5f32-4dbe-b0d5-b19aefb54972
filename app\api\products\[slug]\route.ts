import { NextResponse } from "next/server"
import { fetchProductDetail, mapProductDetailToViewModel } from "@/services/product-detail-api"
import { extractProductIdFromSlug } from "@/utils/product-utils"

export async function GET(
  request: Request,
  { params }: { params: { slug: string } }
) {
  try {
    // Extract the product ID from the slug
    const productId = extractProductIdFromSlug(params.slug)
    
    // Fetch the product details
    const productDetails = await fetchProductDetail(productId)
    
    if (!productDetails || !productDetails.items || productDetails.items.length === 0) {
      return NextResponse.json({ error: "Product not found" }, { status: 404 })
    }
    
    // Map the product details to the view model
    const product = mapProductDetailToViewModel(productDetails)
    
    return NextResponse.json(product)
  } catch (error) {
    console.error("Error fetching product:", error)
    return NextResponse.json({ error: "Failed to fetch product" }, { status: 500 })
  }
}
