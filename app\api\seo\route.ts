import { type NextRequest, NextResponse } from "next/server"
import { getPageSeo, getProductSeo } from "@/services/seo-service"

export async function GET(request: NextRequest) {
  const searchParams = request.nextUrl.searchParams
  const page = searchParams.get("page")
  const productName = searchParams.get("productName")

  if (!page) {
    return NextResponse.json({ error: "Page parameter is required" }, { status: 400 })
  }

  try {
    let seoData

    if (page === "detail" && productName) {
      // For product detail pages
      seoData = await getProductSeo(productName)
    } else {
      // For regular pages
      const pageSeo = await getPageSeo(page)
      seoData = pageSeo?.seo || null
    }

    return NextResponse.json({ seo: seoData })
  } catch (error) {
    console.error("Error fetching SEO data:", error)
    return NextResponse.json({ error: "Failed to fetch SEO data" }, { status: 500 })
  }
}
