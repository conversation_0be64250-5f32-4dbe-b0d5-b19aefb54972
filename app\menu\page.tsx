import type { Metada<PERSON> } from "next"
import { generatePageMetadata } from "@/lib/metadata-utils"
import { getPageH1 } from "@/lib/seo-utils"
import { fetchProducts, mapProductToMenuItem, filterValidProducts } from "@/services/api"
import MenuContent from "@/components/menu-content"

export const revalidate = 3600 // Revalidate every hour

export async function generateMetadata(): Promise<Metadata> {
  // Use "menu" XML page name for the menu page
  return generatePageMetadata("menu")
}

export default async function MenuPage() {
  let menuItems = []
  let error = null

  // Get the H1 from XML
  const h1 = await getPageH1("menu")

  try {
    const data = await fetchProducts()
    // Filter valid products before mapping
    const validProducts = filterValidProducts(data.items)
    menuItems = validProducts.map(mapProductToMenuItem)
  } catch (err) {
    error = "Failed to load menu items. Please try again later."
    console.error(error, err)
  }

  return (
    <>
      {/* Use H1 from XML if available, otherwise use fallback */}
      <h1 className="sr-only">{h1 || "Indian Vegetarian Dishes and Meal Kits in Canada"}</h1>
      <MenuContent menuItems={menuItems} error={error} />
    </>
  )
}
