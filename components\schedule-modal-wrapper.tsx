"use client";

import { useCart } from "@/context/cart-context";
import { useRouter, usePathname } from "next/navigation";
import { useState, useEffect, useRef } from "react";
import { useToast } from "@/context/toast-context";
import ScheduleModal from "./schedule-modal";

type ScheduleModalWrapperProps = {
  isOpen?: boolean;
  onClose?: () => void;
  preventAutoOpen?: boolean;
};

export default function ScheduleModalWrapper({
  isOpen: externalIsOpen,
  onClose: externalOnClose,
  preventAutoOpen = false,
}: ScheduleModalWrapperProps) {
  const {
    deliveryTiming,
    orderType,
    deliveryAddress,
    setDeliveryTiming,
    orderSchedule,
    setAddressError,
  } = useCart();
  const router = useRouter();
  const pathname = usePathname();
  const [internalShowModal, setInternalShowModal] = useState(false);
  const { showToast } = useToast();

  // Track if this is the initial mount
  const isInitialMount = useRef(true);

  // Track user-initiated changes
  const [userChangedTiming, setUserChangedTiming] = useState(false);

  // Determine if we're using external or internal control
  const isControlled = externalIsOpen !== undefined;
  const showModal = isControlled ? externalIsOpen : internalShowModal;

  // Handle modal close based on whether we're using external or internal control
  const handleClose = () => {
    if (isControlled && externalOnClose) {
      externalOnClose();
    } else {
      setInternalShowModal(false);
    }

    // Reset the user change flag when modal is closed
    setUserChangedTiming(false);
  };

  // Listen for changes to deliveryTiming from user interaction
  useEffect(() => {
    // Skip on initial mount
    if (isInitialMount.current) {
      isInitialMount.current = false;
      return;
    }

    // Skip if preventAutoOpen is true and this is not a user-initiated change
    if (preventAutoOpen && !userChangedTiming) {
      return;
    }

    const isScheduledOption =
      deliveryTiming === "later" ||
      deliveryTiming === "weekly" ||
      deliveryTiming === "biweekly";

    if (pathname === "/" && isScheduledOption) {
      // Check if address is required and provided for delivery
      if (orderType === "delivery" && !deliveryAddress) {
      } else {
        // Show modal when switching to a scheduled delivery option
        setAddressError(null);
        setInternalShowModal(true);
      }
    }

    // Reset the user change flag after processing
    setUserChangedTiming(false);
  }, [
    deliveryTiming,
    orderType,
    deliveryAddress,
    pathname,
    setAddressError,
    setDeliveryTiming,
    preventAutoOpen,
    userChangedTiming,
  ]);

  // Expose a method to mark timing changes as user-initiated
  useEffect(() => {
    // Create a custom event listener for delivery timing changes
    const handleDeliveryTimingChange = () => {
      setUserChangedTiming(true);
    };

    // Add event listener
    document.addEventListener(
      "userChangedDeliveryTiming",
      handleDeliveryTimingChange
    );

    // Cleanup
    return () => {
      document.removeEventListener(
        "userChangedDeliveryTiming",
        handleDeliveryTimingChange
      );
    };
  }, []);

  return showModal ? (
    <ScheduleModal isOpen={true} onClose={handleClose} />
  ) : null;
}
