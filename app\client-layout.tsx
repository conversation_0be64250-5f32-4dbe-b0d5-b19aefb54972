"use client"

import type React from "react"
import "./globals.css"
import { Inter } from "next/font/google"
import Header from "@/components/header"
import Footer from "@/components/footer"
import { CartProvider } from "@/context/cart-context"
import { ToastProvider } from "@/context/toast-context"
import { BusinessProvider } from "@/context/business-context"
import { ProductModalProvider } from "@/context/product-modal-context"
import MealBoxSelectorWrapper from "@/components/meal-box-selector-wrapper"
import Cart from "@/components/cart"
import ProductDetailModal from "@/components/product-detail-modal"
import { useProductModal } from "@/context/product-modal-context"
import { SeoProvider } from "@/context/seo-context"

const inter = Inter({
  subsets: ["latin"],
  display: "swap",
})

export default function ClientLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <SeoProvider>
      <div className={inter.className}>
        <ToastProvider>
          <BusinessProvider>
            <CartProvider>
              <ProductModalProvider>
                <Header />
                {children}
                <Footer />
                <MealBoxSelectorWrapper />
                <Cart />
                <ProductDetailModalWrapper />
              </ProductModalProvider>
            </CartProvider>
          </BusinessProvider>
        </ToastProvider>
      </div>
    </SeoProvider>
  )
}

// Wrapper component to access the context
function ProductDetailModalWrapper() {
  const { isModalOpen, selectedProductId, closeProductModal } = useProductModal()
  return <ProductDetailModal isOpen={isModalOpen} onClose={closeProductModal} productId={selectedProductId} />
}
