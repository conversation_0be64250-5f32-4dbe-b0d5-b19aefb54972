import type { UserLocation, AddressDetails } from "@/context/cart-context"
import { BRANCH_ID } from "@/constants/app-constants"

type AddressValidationResponse = {
  checkdeliverychargesbyradiusbit: string
  checkdeliverychargesbyradius: string
  delivery_tax_amount: number
  delivery_charges_area_wise: number
  delivery_charges_area_wise_bit: number
  minimum_spend_delivery: number | null
  discount_charges_delivery: number | null
  grandTotal: number
  calculated_weight: number | null
  out_side_delivery_radius: string | null
  cartSummary: string
  html: string
}

export async function validateDeliveryAddress(
  location: UserLocation,
  addressDetails: AddressDetails,
): Promise<{ isValid: boolean; message: string | null }> {
  try {
    const response = await fetch("https://ezeats.tossdown.site/website/ajax_delivery_charges_by_lat_log", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams({
        latitude: location.lat,
        longitude: location.lng,
        branch_id: BRANCH_ID.toString(),
        postal_code: addressDetails.postalCode || "",
        user_city: addressDetails.city || "",
        country: addressDetails.country || "CA",
        cod: "0",
      }).toString(),
    })

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`)
    }

    const data: AddressValidationResponse = await response.json()

    // Check if the address is outside delivery radius
    if (data.out_side_delivery_radius) {
      return {
        isValid: false,
        message: data.out_side_delivery_radius,
      }
    }

    return {
      isValid: true,
      message: null,
    }
  } catch (error) {
    console.error("Error validating delivery address:", error)
    return {
      isValid: false,
      message: "Unable to validate delivery address. Please try again.",
    }
  }
}
