"use client";

import React from "react";

import Image from "next/image";
import { ChevronDown } from "lucide-react";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import StarRating from "./star-rating";

// Add a utility function to parse descriptions at the top of the file, after the imports
function parseDescription(description = "") {
  let shortDesc = "";
  let longDesc = "";

  // Extract short description
  const shortDescMatch = description.match(/<short_desc>(.*?)<\/short_desc>/s);
  if (shortDescMatch && shortDescMatch[1]) {
    shortDesc = shortDescMatch[1].trim();
  }

  // Extract long description
  const longDescMatch = description.match(/<long_desc>(.*?)<\/long_desc>/s);
  if (longDescMatch && longDescMatch[1]) {
    longDesc = longDescMatch[1].trim();
  }

  // If no tags found, use the entire description as long description
  if (!shortDescMatch && !longDescMatch) {
    longDesc = description;
  }

  return { shortDesc, longDesc };
}

// Replace the cleanBootstrapHTML function with this updated version that preserves the necessary classes
function cleanBootstrapHTML(html: string): string {
  return (
    html
      // Keep the log_des_* classes but remove other classes
      .replace(/\sclass=["'](?!log_des_)[^"']*["']/gi, "")
      // Remove Bootstrap data-* attributes (like data-toggle, data-target)
      .replace(/\sdata-[\w-]+=["'][^"']*["']/gi, "")
      // Remove aria-* attributes
      .replace(/\saria-[\w-]+=["'][^"']*["']/gi, "")
      // Remove empty spans or tags left over
      .replace(/<span[^>]*>\s*<\/span>/gi, "")
      // Remove any leftover multiple spaces
      .replace(/\s{2,}/g, " ")
      .trim()
  );
}

// Fix the DOMParser issue by adding client-side check
// Replace the parseDetailedSections function with this updated version:

function parseDetailedSections(description = "") {
  // Default empty sections
  const sections = {
    overview: {
      html: "",
      text: "",
    },
    ingredients: {
      html: "",
      text: "",
      items: [] as string[],
      allergens: [] as string[],
      mayContain: [] as string[],
    },
    nutritionFacts: {
      html: "",
      text: "",
      items: {} as Record<string, Record<string, string>>,
    },
    heatingInstructions: {
      html: "",
      text: "",
      general: "",
      microwave: "",
      oven: "",
    },
  };

  if (!description) return sections;

  try {
    // Check if we're in a browser environment before using DOMParser
    if (typeof window === "undefined") {
      return sections;
    }

    // Create a temporary DOM element to parse the HTML
    const parser = new DOMParser();
    const doc = parser.parseFromString(description, "text/html");

    // Rest of the function remains the same...
    // Find all elements with the class 'log_des_toggle_data'
    const elements = doc.querySelectorAll(".log_des_toggle_data");

    // Extract the content from each section
    if (elements.length > 0) {
      sections.overview.html = elements[0].innerHTML;
      sections.overview.text = elements[0].textContent || "";
    }

    // Process ingredients section
    if (elements.length > 1) {
      sections.ingredients.html = elements[1].innerHTML;
      sections.ingredients.text = elements[1].textContent || "";

      // Extract ingredients list
      const ingredientsText = sections.ingredients.text;

      // Look for allergens
      const allergensMatch = ingredientsText.match(/allergens:([^.]+)/i);
      if (allergensMatch && allergensMatch[1]) {
        sections.ingredients.allergens = allergensMatch[1]
          .split(",")
          .map((item) => item.trim());
      }

      // Look for "may contain"
      const mayContainMatch = ingredientsText.match(/may contain:([^.]+)/i);
      if (mayContainMatch && mayContainMatch[1]) {
        sections.ingredients.mayContain = mayContainMatch[1]
          .split(",")
          .map((item) => item.trim());
      }

      // Extract main ingredients by removing allergens and "may contain" sections
      const mainIngredients = ingredientsText
        .replace(/allergens:([^.]+)/i, "")
        .replace(/may contain:([^.]+)/i, "")
        .trim();

      sections.ingredients.items = mainIngredients
        .split(",")
        .map((item) => item.trim())
        .filter((item) => item);
    }

    // Process nutrition facts section
    if (elements.length > 2) {
      sections.nutritionFacts.html = elements[2].innerHTML;
      sections.nutritionFacts.text = elements[2].textContent || "";

      // Try to extract nutrition table data
      const nutritionTable = elements[2].querySelector("table");
      if (nutritionTable) {
        const rows = nutritionTable.querySelectorAll("tr");
        let currentCategory = "General";

        rows.forEach((row) => {
          const cells = row.querySelectorAll("td, th");
          if (cells.length >= 2) {
            const firstCell = cells[0].textContent?.trim() || "";

            // Check if this is a category header
            if (
              cells.length === 1 ||
              (firstCell && !cells[1].textContent?.trim())
            ) {
              currentCategory = firstCell;
              sections.nutritionFacts.items[currentCategory] = {};
            } else {
              // This is a data row
              const label = firstCell;
              const value = cells[1].textContent?.trim() || "";

              if (!sections.nutritionFacts.items[currentCategory]) {
                sections.nutritionFacts.items[currentCategory] = {};
              }

              if (label) {
                sections.nutritionFacts.items[currentCategory][label] = value;
              }
            }
          }
        });
      }
    }

    // Process heating instructions section
    if (elements.length > 3) {
      sections.heatingInstructions.html = elements[3].innerHTML;
      sections.heatingInstructions.text = elements[3].textContent || "";

      // Try to extract specific heating instructions
      const text = sections.heatingInstructions.text;

      // Extract general instructions (usually the first paragraph)
      const generalMatch = text.match(/^([^.]+\.)/i);
      if (generalMatch && generalMatch[1]) {
        sections.heatingInstructions.general = generalMatch[1].trim();
      }

      // Extract microwave instructions
      const microwaveMatch = text.match(/microwave:([^.]+\.)/i);
      if (microwaveMatch && microwaveMatch[1]) {
        sections.heatingInstructions.microwave = microwaveMatch[1].trim();
      }

      // Extract oven instructions
      const ovenMatch = text.match(/oven:([^.]+\.)/i);
      if (ovenMatch && ovenMatch[1]) {
        sections.heatingInstructions.oven = ovenMatch[1].trim();
      }
    }
  } catch (error) {
    console.error("Error parsing detailed sections:", error);
  }

  return sections;
}

// Update the ProductDetailProps type to include description
type ProductDetailProps = {
  product: {
    id: number;
    name: string;
    image: string;
    price: number;
    isVeg: boolean;
    rating: number;
    description: string;
    inStock?: boolean;
  };
  relatedProducts: Array<{
    id: number;
    name: string;
    image: string;
    price: number;
    isVeg: boolean;
    rating: number;
    inStock?: boolean;
  }>;
};

type CollapsibleSectionProps = {
  title: string;
  children: React.ReactNode;
  defaultOpen?: boolean;
};

// Update the CollapsibleSection component to use the log_des_* classes
const CollapsibleSection = ({
  title,
  children,
  defaultOpen = false,
}: CollapsibleSectionProps) => {
  const [isOpen, setIsOpen] = useState(defaultOpen);

  return (
    <div className="log_des_acc_main">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className={`log_des_acc_header ${
          isOpen ? "" : "collapsed"
        } flex items-center justify-between w-full text-left`}
      >
        <span className="title">{title}</span>
        <div className="acc_icon">
          <ChevronDown
            size={20}
            className={`rotate-icon transition-transform ${
              isOpen ? "rotate-180" : ""
            }`}
          />
        </div>
      </button>
      <div
        className={`log_des_toggle_data mt-4 ${isOpen ? "block" : "hidden"}`}
      >
        {children}
      </div>
    </div>
  );
};

// Update the component to display short and long descriptions
export default function ProductDetail({
  product,
  relatedProducts,
}: ProductDetailProps) {
  const router = useRouter();
  const [detailedSections, setDetailedSections] = useState({
    overview: { html: "", text: "" },
    ingredients: {
      html: "",
      text: "",
      items: [],
      allergens: [],
      mayContain: [],
    },
    nutritionFacts: { html: "", text: "", items: {} },
    heatingInstructions: {
      html: "",
      text: "",
      general: "",
      microwave: "",
      oven: "",
    },
  });

  // Parse the description
  const { shortDesc, longDesc } = parseDescription(product.description);

  // Parse detailed sections when product changes
  useEffect(() => {
    if (product?.description) {
      setDetailedSections(parseDetailedSections(product.description));
    }
  }, [product]);

  return (
    <main className="min-h-screen py-8">
      <div className="container mx-auto px-4 max-w-3xl">
        {/* Product header section */}
        <div className="flex flex-col md:flex-row gap-8 mb-8">
          {/* Product image */}
          <div className="w-full md:w-1/2">
            <div className="relative aspect-square rounded-lg overflow-hidden">
              <Image
                src={product.image || "/placeholder.svg"}
                alt={product.name}
                fill
                className="object-cover"
                sizes="(max-width: 768px) 100vw, 50vw"
                priority
              />
            </div>
          </div>

          {/* Product info */}
          <div className="w-full md:w-1/2">
            <h1 className="text-3xl font-bold mb-2 uppercase">
              {product.name}
            </h1>

            {/* Ratings */}
            <div className="flex items-center mb-2">
              <StarRating rating={product.rating} starSize={20} />
            </div>

            {/* Price */}
            <div className="text-lg font-bold mb-4">
              CAD {product.price.toFixed(2)}
            </div>

            {/* Add short description below price */}
            {shortDesc && (
              <div
                className="text-gray-700 mb-4"
                dangerouslySetInnerHTML={{
                  __html: cleanBootstrapHTML(shortDesc),
                }}
              />
            )}
          </div>
        </div>

        {/* Add long description below product image */}
        {longDesc && (
          <div className="mb-8">
            <h2 className="text-xl font-medium mb-2">Description</h2>
            <div
              className="text-gray-700"
              dangerouslySetInnerHTML={{ __html: cleanBootstrapHTML(longDesc) }}
            />
          </div>
        )}

        {/* Collapsible sections */}
        <div className="mt-8">
          <CollapsibleSection title="OVERVIEW" defaultOpen={true}>
            {detailedSections.overview.text ? (
              <p className="text-gray-700">{detailedSections.overview.text}</p>
            ) : detailedSections.overview.html ? (
              <div
                className="log_des_data_parent"
                dangerouslySetInnerHTML={{
                  __html: cleanBootstrapHTML(detailedSections.overview.html),
                }}
              />
            ) : null}
          </CollapsibleSection>

          <CollapsibleSection title="INGREDIENTS">
            {detailedSections.ingredients.items.length > 0 ? (
              <div className="space-y-4">
                <div>
                  <p className="text-gray-700">
                    {detailedSections.ingredients.items.join(", ")}
                  </p>
                </div>

                {detailedSections.ingredients.allergens.length > 0 && (
                  <div>
                    <span className="font-medium">Allergens: </span>
                    <span className="text-gray-700">
                      {detailedSections.ingredients.allergens.join(", ")}
                    </span>
                  </div>
                )}

                {detailedSections.ingredients.mayContain.length > 0 && (
                  <div>
                    <span className="font-medium">May contain: </span>
                    <span className="text-gray-700">
                      {detailedSections.ingredients.mayContain.join(", ")}
                    </span>
                  </div>
                )}
              </div>
            ) : detailedSections.ingredients.html ? (
              <div
                className="log_des_data_parent"
                dangerouslySetInnerHTML={{
                  __html: cleanBootstrapHTML(detailedSections.ingredients.html),
                }}
              />
            ) : null}
          </CollapsibleSection>

          <CollapsibleSection title="NUTRITION FACTS">
            {Object.keys(detailedSections.nutritionFacts.items).length > 0 ? (
              <div className="border border-gray-200 rounded-lg p-6">
                <h3 className="text-xl font-medium text-center mb-6">
                  NUTRITION FACTS
                </h3>

                {Object.entries(detailedSections.nutritionFacts.items).map(
                  ([category, items]) => (
                    <div key={category} className="mb-6">
                      {category !== "General" && (
                        <h4 className="font-medium text-lg mb-2">{category}</h4>
                      )}

                      <div className="grid grid-cols-2 gap-2">
                        {Object.entries(items).map(([label, value]) => (
                          <React.Fragment key={label}>
                            <div className="font-medium capitalize">
                              {label}
                            </div>
                            <div>{value}</div>
                          </React.Fragment>
                        ))}
                      </div>
                    </div>
                  )
                )}
              </div>
            ) : detailedSections.nutritionFacts.html ? (
              <div
                className="log_des_data_parent"
                dangerouslySetInnerHTML={{
                  __html: cleanBootstrapHTML(
                    detailedSections.nutritionFacts.html
                  ),
                }}
              />
            ) : null}
          </CollapsibleSection>

          <CollapsibleSection title="HEATING INSTRUCTION">
            {detailedSections.heatingInstructions.general ||
            detailedSections.heatingInstructions.microwave ||
            detailedSections.heatingInstructions.oven ? (
              <div className="space-y-6">
                {detailedSections.heatingInstructions.general && (
                  <p className="text-gray-700">
                    {detailedSections.heatingInstructions.general}
                  </p>
                )}

                {detailedSections.heatingInstructions.microwave && (
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 flex-shrink-0">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.5"
                      >
                        <rect x="2" y="4" width="20" height="16" rx="2" />
                        <path d="M4 8h.01M4 12h.01M4 16h.01M8 6h8M8 18h8" />
                      </svg>
                    </div>
                    <div>
                      <div className="font-medium mb-1">MICROWAVE:</div>
                      <p className="text-gray-700">
                        {detailedSections.heatingInstructions.microwave}
                      </p>
                    </div>
                  </div>
                )}

                {detailedSections.heatingInstructions.oven && (
                  <div className="flex items-start space-x-4">
                    <div className="w-12 h-12 flex-shrink-0">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="1.5"
                      >
                        <rect x="2" y="4" width="20" height="16" rx="2" />
                        <line x1="6" y1="12" x2="18" y2="12" />
                        <line x1="6" y1="8" x2="18" y2="8" />
                        <line x1="6" y1="16" x2="18" y2="16" />
                      </svg>
                    </div>
                    <div>
                      <div className="font-medium mb-1">OVEN:</div>
                      <p className="text-gray-700">
                        {detailedSections.heatingInstructions.oven}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            ) : detailedSections.heatingInstructions.html ? (
              <div
                className="log_des_data_parent"
                dangerouslySetInnerHTML={{
                  __html: cleanBootstrapHTML(
                    detailedSections.heatingInstructions.html
                  ),
                }}
              />
            ) : null}
          </CollapsibleSection>
        </div>
      </div>
    </main>
  );
}
