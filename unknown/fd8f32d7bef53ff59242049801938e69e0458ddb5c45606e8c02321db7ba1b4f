"use client"

import { useEffect, useState, useRef } from "react"
import { Loader2 } from "lucide-react"
import { useAuth } from "@/context/auth-context"

type ProfileSectionProps = {
  section: string
  activeTab?: string
  view?: string
  title: string
}

export default function ProfileSection({ section, activeTab, view, title }: ProfileSectionProps) {
  const { user } = useAuth()
  const [isLoading, setIsLoading] = useState(true)
  const [scriptError, setScriptError] = useState<string | null>(null)
  const [isCleaningUp, setIsCleaningUp] = useState(false)
  const mountedRef = useRef(false)
  const scriptsLoadedRef = useRef(false)

  // Generate a unique key for remounting the component
  const pageKey = useRef(`${section}-${Date.now()}`).current

  // Reset the profile application
  const resetProfile = () => {
    // Clear any existing profile app elements
    const rootElement = document.getElementById("root")
    if (rootElement) {
      rootElement.innerHTML = ""
    }

    // Reset scripts loaded flag
    scriptsLoadedRef.current = false

    // Remove any previously added scripts
    const oldScripts = document.querySelectorAll("script[data-profile-script]")
    oldScripts.forEach((script) => script.remove())
  }

  // Cleanup function to remove all profile-related elements
  const cleanupProfile = () => {
    if (isCleaningUp) return
    setIsCleaningUp(true)

    // Remove any toast elements that might have been added by the profile app
    const toastElements = document.querySelectorAll(
      '[class*="toast"], [id*="toast"], [class*="notification"], [id*="notification"]',
    )
    toastElements.forEach((el) => el.remove())

    // Remove any modal or overlay elements
    const overlayElements = document.querySelectorAll(
      '[class*="modal"], [id*="modal"], [class*="overlay"], [id*="overlay"], [class*="backdrop"], [id*="backdrop"]',
    )
    overlayElements.forEach((el) => el.remove())

    // Remove any profile-specific elements that might have been added to the body
    const profileElements = document.querySelectorAll(
      '[class*="profile"], [id*="profile"], [class*="ordrz"], [id*="ordrz"]',
    )
    profileElements.forEach((el) => el.remove())

    // Remove any scripts added by the profile app
    const profileScripts = document.querySelectorAll("script[data-profile-script]")
    profileScripts.forEach((script) => script.remove())

    // Clear the root element
    const rootElement = document.getElementById("root")
    if (rootElement) {
      rootElement.innerHTML = ""
    }

    // Reset the businessInfo object
    if (window.businessInfo) {
      window.businessInfo = {} as any
    }

    // Remove any event listeners added by the profile app (as much as possible)
    const cleanup = () => {
      try {
        // This is a best effort to remove event listeners
        const oldAddEventListener = window.addEventListener
        const oldRemoveEventListener = window.removeEventListener

        // Restore original methods if they were overridden
        if (window.addEventListener !== oldAddEventListener) {
          window.addEventListener = oldAddEventListener
        }

        if (window.removeEventListener !== oldRemoveEventListener) {
          window.removeEventListener = oldRemoveEventListener
        }
      } catch (e) {
        console.error("Error cleaning up event listeners:", e)
      }
    }

    cleanup()
    setIsCleaningUp(false)
  }

  const initializeProfile = () => {
    // Reset any previous profile state
    resetProfile()

    // Set loading state
    setIsLoading(true)
    setScriptError(null)

    // Format user data for the external profile system
    const businessInfo = {
      businessId: "12536",
      branchId: "18784",
      brandId: "458",
      source: "ezeats",
      user: {
        id: user?.id || "",
        name: user?.name || "",
        email: user?.email || "",
        phone: user?.phone || "",
        token: user?.authToken || "",
      },
      section: section,
      activeTab: activeTab || section,
      view: view || section,
    }

    // Make the profile data available to the external profile application
    window.businessInfo = businessInfo

    // Load external scripts programmatically
    if (!scriptsLoadedRef.current) {
      const loadScript = (src: string) => {
        return new Promise<void>((resolve, reject) => {
          const script = document.createElement("script")
          script.src = src
          script.async = true
          script.defer = true
          script.setAttribute("data-profile-script", "true")

          script.onload = () => {
            resolve()
          }

          script.onerror = (error) => {
            console.error(`Error loading script: ${src}`, error)
            reject(new Error(`Failed to load script: ${src}`))
          }

          document.body.appendChild(script)
        })
      }

      // Load scripts in sequence
      Promise.all([
        loadScript("https://profile.ordrz.com/static/js/main.b75be690.js"),
        loadScript("https://profile.ordrz.com/static/js/179.c3afeb96.chunk.js"),
      ])
        .then(() => {
          scriptsLoadedRef.current = true
          setIsLoading(false)
        })
        .catch((error) => {
          console.error("Failed to load profile scripts:", error)
          setScriptError("Failed to load profile application. Please try refreshing the page")
          setIsLoading(false)
        })
    } else {
      // Scripts already loaded, just hide loading
      setIsLoading(false)
    }
  }

  // Initialize on first mount and when dependencies change
  useEffect(() => {
    // Mark component as mounted
    mountedRef.current = true

    // Initialize profile
    initializeProfile()

    // Cleanup function
    return () => {
      mountedRef.current = false
      // Clean up profile elements when component unmounts
      cleanupProfile()
    }
  }, [user, section, activeTab, view]) // Re-initialize when these props change

  // Add event listener for page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === "visible" && mountedRef.current) {
        initializeProfile()
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [])

  // Add event listener for navigation
  useEffect(() => {
    // Listen for navigation events
    const handleBeforeUnload = () => {
      cleanupProfile()
    }

    // Add event listener for page unload
    window.addEventListener("beforeunload", handleBeforeUnload)

    // Clean up on component unmount
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload)
      cleanupProfile()
    }
  }, [])

  // Add a cleanup effect that runs when the component unmounts
  useEffect(() => {
    return () => {
      cleanupProfile()
    }
  }, [])

  return (
    <div className="min-h-screen" key={pageKey}>
      {/* Include external CSS */}
      <link rel="stylesheet" href="https://profile.ordrz.com/static/css/main.a666252e.css" />

      {/* Include Google Fonts */}
      <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans&display=swap" rel="stylesheet" />

      {/* Simple loading indicator instead of skeleton */}
      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="w-12 h-12 animate-spin text-gray-400" />
        </div>
      )}

      {/* Show error message if scripts failed to load */}
      {scriptError && (
        <div className="text-center py-10">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h2 className="text-xl font-semibold text-red-700 mb-2">Error Loading {title}</h2>
            <p className="text-red-600 mb-4">{scriptError}</p>
            <button
              onClick={() => {
                setIsLoading(true)
                setScriptError(null)
                scriptsLoadedRef.current = false
                initializeProfile()
              }}
              className="bg-black text-white px-6 py-2 rounded-md"
            >
              Try Again
            </button>
          </div>
        </div>
      )}

      {/* This is the container where the external React app will mount */}
      <div id="root"></div>
    </div>
  )
}
