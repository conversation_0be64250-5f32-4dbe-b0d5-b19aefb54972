"use client"

import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { usePathname } from "next/navigation"

type BreadcrumbItem = {
  label: string
  href: string
  isCurrent?: boolean
}

type BreadcrumbsProps = {
  items: BreadcrumbItem[]
  className?: string
}

export default function Breadcrumbs({ items, className = "" }: BreadcrumbsProps) {
  return (
    <nav aria-label="Breadcrumb" className={`text-sm ${className}`}>
      <ol className="flex items-center flex-wrap">
        {items.map((item, index) => (
          <li key={item.href} className="flex items-center">
            {index > 0 && <ChevronRight size={16} className="mx-2 text-gray-400" />}

            {item.isCurrent ? (
              <span className="font-medium text-gray-800" aria-current="page">
                {item.label}
              </span>
            ) : (
              <Link href={item.href} className="text-gray-600 hover:text-gray-900 hover:underline">
                {item.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  )
}

export function useBreadcrumbs(productName?: string) {
  const pathname = usePathname()

  // Default breadcrumbs for home
  const breadcrumbs: BreadcrumbItem[] = [{ label: "Home", href: "/" }]

  // Add menu breadcrumb if we're in a product page
  if (pathname?.startsWith("/product/")) {
    breadcrumbs.push({ label: "Menu", href: "/our-menu" })

    // Add the product name if available
    if (productName) {
      breadcrumbs.push({
        label: productName,
        href: pathname,
        isCurrent: true,
      })
    }
  }

  return breadcrumbs
}
