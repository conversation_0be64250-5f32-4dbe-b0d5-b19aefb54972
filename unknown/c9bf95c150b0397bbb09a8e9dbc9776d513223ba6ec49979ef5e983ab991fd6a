import Cookies from "js-cookie"

// Types
export interface UserAuthResponse {
  user_fullname?: string
  user_email?: string
  user_cphone?: string
  user_id?: string
  td_user_id?: string
  token?: string
  refresh_token?: string
  payment_settings?: {
    stripe?: string
  }
}

export type PaymentDetailsType = {}

export interface UserAuthDetails {
  name?: string
  email?: string
  phone?: string
  userId?: string
  tdUserId?: string
  authToken?: string
  renewAuthToken?: string
  paymentDetails?: {
    stripe: {
      stripeCustomerId: string
    }
  }
}

export type GenericMap = Record<string, any>

export interface UserSavedData extends UserAuthDetails {
  // Any additional fields that might be in the saved data
}

// Cookie settings
const cookiesExpiry = 30 // 30 days
const COOKIE_NAME = "tdUDetails"

// Get user data from cookies
export const getCookies = (): UserSavedData | null => {
  try {
    // Check if we're in a browser environment
    if (typeof window === "undefined" || typeof document === "undefined") {
      return null
    }

    const cookieData = Cookies.get(COOKIE_NAME)
    if (cookieData) {
      try {
        const parsedData = JSON.parse(cookieData)

        // Explicitly check for userId
        if (!parsedData.userId) {
          console.warn("Cookie exists but userId is missing")
          return null
        }

        return parsedData
      } catch (e) {
        console.error("Error parsing cookie data:", e)
        return null
      }
    }
    return null
  } catch (error) {
    console.error("Error in getCookies:", error)
    return null
  }
}

// Update cookies with new data
export const updateCookies = (details: GenericMap): void => {
  try {
    // Check if we're in a browser environment
    if (typeof window === "undefined" || typeof document === "undefined") {
      return
    }

    const userDetails: UserSavedData | {} = getCookies() || {}

    // set user details to cookies
    Cookies.set(
      COOKIE_NAME,
      JSON.stringify({
        ...userDetails,
        ...details,
      }),
      {
        expires: cookiesExpiry,
        path: "/",
        sameSite: "strict",
      },
    )
  } catch (error) {
    console.error("Error in updateCookies:", error)
  }
}

// Set user auth response in cookies
export const setUserAuthResponse = (paymentDetails: PaymentDetailsType, userDetails: UserAuthResponse): void => {
  try {
    // Check if we're in a browser environment
    if (typeof window === "undefined" || typeof document === "undefined") {
      return
    }

    // Ensure we have a user_id before setting the cookie
    if (!userDetails?.user_id) {
      console.error("Cannot set user auth: missing user_id")
      return
    }

    const userAuthResponse: UserAuthDetails = {
      name: userDetails?.user_fullname,
      email: userDetails?.user_email,
      phone: userDetails?.user_cphone,

      userId: userDetails?.user_id,
      tdUserId: userDetails?.td_user_id,
      authToken: userDetails?.token,
      renewAuthToken: userDetails?.refresh_token,

      // stripeCustomerId => if customer is registered on stripe (card saved on stripe)
      paymentDetails: {
        stripe: {
          stripeCustomerId: userDetails?.payment_settings?.stripe || "",
        },
      },
    }

    // set data in cookies
    updateCookies(userAuthResponse)
  } catch (error) {
    console.error("Error in setUserAuthResponse:", error)
  }
}

// Clear user auth data from cookies - completely revised
export const clearUserAuth = (): boolean => {
  try {
    // Check if we're in a browser environment
    if (typeof window === "undefined" || typeof document === "undefined") {
      return false
    }

    // Try multiple approaches to ensure cookie is removed

    // Approach 1: Using js-cookie with various options
    Cookies.remove(COOKIE_NAME)
    Cookies.remove(COOKIE_NAME, { path: "/" })

    // Approach 2: Direct document.cookie manipulation
    document.cookie = `${COOKIE_NAME}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`

    // Approach 3: Set to empty with immediate expiry
    Cookies.set(COOKIE_NAME, "", { expires: new Date(0), path: "/" })

    // Verify cookie is removed
    const cookieExists = Cookies.get(COOKIE_NAME)

    // Also clear localStorage in case it's being used
    localStorage.removeItem(COOKIE_NAME)

    return !cookieExists
  } catch (error) {
    console.error("Error clearing cookies:", error)
    return false
  }
}
