"use client"

import type React from "react"

import { useAuth } from "@/context/auth-context"
import { clearUserAuth } from "@/utils/auth-utils"
import { useRouter } from "next/navigation"

export default function LogoutButton() {
  const { logout } = useAuth()
  const router = useRouter()

  const handleLogout = (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    console.log("Direct logout button clicked")

    // Try direct cookie clearing first
    const cleared = clearUserAuth()
    console.log("Cookies directly cleared:", cleared)

    // Then call the context logout function
    logout()

    // Use Next.js router instead of relying on page reload
    router.push("/")
  }

  return (
    <button onClick={handleLogout} className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600">
      Force Logout
    </button>
  )
}
