/* Our Kitchen Page Specific Styles */

.our-kitchen-hero {
  width: 100%;
  position: relative;
}

.our-kitchen-hero img {
  width: 100%;
  object-fit: cover;
}

.about-section {
  width: 100%;
  margin-top: 2rem;
}

.about-section img {
  width: 100%;
  object-fit: cover;
}

/* Call to Action Section Styles */
.cta-section {
  padding: 4rem 0;
}

.say-goodbye {
  display: flex;
  align-items: flex-start;
  margin-bottom: 1rem;
}

.say-goodbye h2 {
  font-family: var(--font-anton);
  font-size: 5rem;
  font-weight: 900;
  letter-spacing: -0.02em;
  line-height: 1;
  text-transform: uppercase;
}

.say-goodbye-text {
  margin-left: 1rem;
  margin-top: 1rem;
}

.say-goodbye-text p {
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
}

.hello-section {
  display: flex;
  align-items: flex-start;
  margin-top: 1rem;
}

.hello-section h2 {
  font-family: var(--font-anton);
  font-size: 5rem;
  font-weight: 900;
  letter-spacing: -0.02em;
  line-height: 1;
  text-transform: uppercase;
}

.hello-text {
  margin-left: 1rem;
  margin-top: 1rem;
}

.hello-text p {
  font-size: 1.5rem;
  font-weight: 500;
  line-height: 1.2;
}

.tagline {
  font-size: 1.25rem;
  font-weight: 500;
  margin-top: 2rem;
  margin-left: 0;
}

/* Join Us Section */
.join-us {
  display: flex;
  margin-top: 6rem;
  margin-bottom: 4rem;
}

.join-us h3 {
  font-family: var(--font-anton);
  font-size: 4rem;
  font-weight: 900;
  letter-spacing: -0.02em;
  line-height: 1;
  text-transform: uppercase;
}

.join-us-content {
  display: flex;
}

.vertical-line {
  border-left: 4px solid #ffcc00;
  margin: 0 1.5rem;
}

.join-us-text {
  max-width: 32rem;
}

.join-us-text p {
  color: #4b5563;
  font-size: 1.125rem;
  line-height: 1.5;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .say-goodbye h2,
  .hello-section h2 {
    font-size: 4rem;
  }

  .say-goodbye-text p,
  .hello-text p {
    font-size: 1.25rem;
  }

  .join-us h3 {
    font-size: 3rem;
  }
}

@media (max-width: 768px) {
  .say-goodbye h2,
  .hello-section h2 {
    font-size: 3rem;
  }

  .say-goodbye-text p,
  .hello-text p {
    font-size: 1.125rem;
  }

  .tagline {
    font-size: 1.125rem;
  }

  .join-us {
    flex-direction: column;
  }

  .join-us h3 {
    font-size: 2.5rem;
    margin-bottom: 1.5rem;
  }

  .vertical-line {
    display: none;
  }
}

@media (max-width: 640px) {
  .say-goodbye h2,
  .hello-section h2 {
    font-size: 2.5rem;
  }

  .say-goodbye-text p,
  .hello-text p {
    font-size: 1rem;
  }

  .tagline {
    font-size: 1rem;
  }

  .join-us h3 {
    font-size: 2rem;
  }

  .join-us-text p {
    font-size: 1rem;
  }
}
