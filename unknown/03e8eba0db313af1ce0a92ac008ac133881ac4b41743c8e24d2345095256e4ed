"use client"

import type { BoxSize } from "@/context/cart-context"
import { useState, useRef, useEffect } from "react"

type MealBoxSelectorProps = {
  isOpen: boolean
  onClose: () => void
  onSelect: (size: BoxSize) => void
}

export default function MealBoxSelector({ isOpen, onClose, onSelect }: MealBoxSelectorProps) {
  const modalRef = useRef<HTMLDivElement>(null)
  const [selectedSize, setSelectedSize] = useState<BoxSize>(6)

  // Prevent clicks inside the modal from propagating
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (modalRef.current && modalRef.current.contains(e.target as Node)) {
        e.stopPropagation()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClick, true)
    }

    return () => {
      document.removeEventListener("mousedown", handleClick, true)
    }
  }, [isOpen])

  if (!isOpen) return null

  const handleSelect = () => {
    onSelect(selectedSize)
    onClose()
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-start justify-center pt-20"
      onClick={(e) => e.stopPropagation()}
    >
      <div ref={modalRef} className="bg-white w-full max-w-md overflow-hidden rounded-md shadow-xl">
        {/* Header */}
        <div className="bg-yellow-400 p-4 text-center">
          <h2 className="text-2xl font-bold">
            <img
              width="101"
              src="https://images-beta.tossdown.com/site/7755023a-890f-4569-9882-20e10f9e53be.webp"
              alt="EZeats"
              className="mx-auto"
            />
          </h2>
        </div>

        {/* Content */}
        <div className="p-8 text-center">
          <h4 className="text-2xl font-normal leading-[30px] text-center font-anton uppercase mb-6">
            Choose how many meals you want to order
          </h4>

          <div className="bg-gray-200 rounded-full p-1 flex justify-between mb-8">
            <button
              className={`py-3 px-6 rounded-full font-bold text-xl w-1/2 transition-colors ${
                selectedSize === 6 ? "bg-black text-white shadow-md" : ""
              }`}
              onClick={() => setSelectedSize(6)}
            >
              6
            </button>
            <button
              className={`py-3 px-6 rounded-full font-bold text-xl w-1/2 transition-colors ${
                selectedSize === 12 ? "bg-black text-white shadow-md" : ""
              }`}
              onClick={() => setSelectedSize(12)}
            >
              12
            </button>
          </div>

          <button onClick={handleSelect} className="bg-black text-white font-bold py-3 px-8 rounded-full w-full">
            CONTINUE
          </button>
        </div>
      </div>
    </div>
  )
}
