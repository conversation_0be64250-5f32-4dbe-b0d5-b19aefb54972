.privacy_policy_page {
  display: block;
  width: 100%;
  padding: 60px 15px 80px 15px; /* Increased padding top and bottom */
}

.privacy_policy_detail {
  display: grid;
  gap: 20px 0px;
  width: 100%;
}

.privacy_policy_detail_description {
  display: grid;
  gap: 10px 0px;
  width: 100%;
}

.privacy_policy_detail_description p {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 26px;
  color: rgba(0, 0, 0, 0.7);
  width: 100%;
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
}

.privacy_policy_detail_two p a {
  color: rgba(0, 0, 0, 0.7);
  text-decoration: none;
  font-weight: bold;
}

.privacy_policy_detail_two {
  display: grid;
  gap: 10px 0px;
  width: 100%;
}

.privacy_policy_detail_two h2 {
  font-style: normal;
  font-weight: 400 !important;
  font-size: 20px;
  line-height: 24px;
  letter-spacing: 0.05em;
  text-transform: uppercase;
  color: #000000;
  margin: 0px !important;
  font-family: var(--font-bebas-neue), Impact, sans-serif;
}

.anton-font {
  font-family: var(--font-anton), Impact, sans-serif !important;
  font-weight: 400 !important;
  font-size: 20px !important;
  line-height: 24px !important;
  letter-spacing: 0.05em;
}

.privacy_policy_detail_two p {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 26px;
  color: rgba(0, 0, 0, 0.7);
  width: 100%;
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
}

.privacy_policy_detail_two ul {
  display: grid;
  gap: 10px 0px;
  width: 100%;
  padding: 0px 0px 0px 17px;
}

.privacy_policy_detail_two ul li {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 26px;
  color: rgba(0, 0, 0, 0.7);
  width: 100%;
  list-style: square;
  text-align: left;
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
}

.privacy_policy_detail_two ul li::marker {
  color: #196f53 !important;
}

.privacy_policy_detail_two ol {
  display: grid;
  gap: 10px 0px;
  width: 100%;
  padding: 0px 0px 0px 17px;
  list-style-type: decimal !important;
}

.privacy_policy_detail_two ol li {
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  line-height: 26px;
  color: rgba(0, 0, 0, 0.7);
  width: 100%;
  text-align: left;
  font-family: var(--font-poppins), Arial, Helvetica, sans-serif;
  list-style-type: decimal !important;
}

.privacy_policy_detail_two ol li::marker {
  color: #196f53 !important;
}

/* Added page title styling */
.terms_page_title {
  font-family: var(--font-anton), Impact, sans-serif;
  font-size: 32px;
  text-align: center;
  margin-bottom: 40px;
  color: #000;
}

@media (max-width: 1024px) {
  /* Responsive styles for large tablets and small desktops */
  .privacy_policy_page {
    padding: 50px 15px 70px 15px;
  }
}

@media (max-width: 768px) {
  /* Responsive styles for tablets */
  .privacy_policy_page {
    padding: 40px 15px 60px 15px;
  }

  .terms_page_title {
    font-size: 28px;
    margin-bottom: 30px;
  }

  .anton-font {
    font-size: 18px !important;
    line-height: 22px !important;
  }
}

@media (max-width: 540px) {
  .privacy_policy_detail_description {
    text-align: center;
  }
  .privacy_policy_detail_two {
    text-align: center;
  }

  .privacy_policy_page {
    padding: 30px 15px 50px 15px;
  }

  .terms_page_title {
    font-size: 24px;
    margin-bottom: 25px;
  }

  .anton-font {
    font-size: 16px !important;
    line-height: 20px !important;
  }
}

@media (max-width: 450px) {
  /* Responsive styles for small mobile devices */
  .privacy_policy_page {
    padding: 25px 15px 40px 15px;
  }
}
