export interface ProductImage {
  image_thumbnail: string
  image: string
  image_id: string
  image_position: string
}

export interface ProductReview {
  id: string
  user_name: string
  date: string
  rating: number
  comment: string
}

export interface Product {
  menu_item_id: string
  name: string
  price: string
  currency: string
  desc: string
  category: string
  image: string
  large_image: string
  product_rating: string
  sku: string
  status: string
  images: ProductImage[]
  inv_limit: number
  min_qty: string
  display_source: string
  reviews?: ProductReview[]
  attributes?: Array<Record<string, string>>
  featured?: string
}

export interface ProductsResponse {
  items: Product[]
  status: string
  message: string
}
