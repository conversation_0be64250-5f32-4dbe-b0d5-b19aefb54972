import { NextResponse } from "next/server"
import { BUSINESS_ID, BRANCH_ID } from "@/constants/app-constants"

export async function POST(request: Request) {
  try {
    const data = await request.json()

    // More detailed validation with specific error messages
    if (!data.name) {
      return NextResponse.json({ status: "error", message: "Name is required" }, { status: 400 })
    }

    if (!data.rating) {
      return NextResponse.json({ status: "error", message: "Rating is required" }, { status: 400 })
    }

    if (!data.comment) {
      return NextResponse.json({ status: "error", message: "Comment is required" }, { status: 400 })
    }

    if (!data.productId) {
      return NextResponse.json({ status: "error", message: "Product ID is required" }, { status: 400 })
    }

    // Format the data for the external API
    const reviewData = {
      rid: BUSINESS_ID,
      uname: data.name.trim(),
      uid: data.userId || "", // If user is logged in
      email: data.email?.trim() || "",
      product_rating: data.rating.toString(),
      product_id: data.productId.toString(), // Ensure it's a string
      phone: data.phone?.trim() || "",
      phone_no: data.phone?.trim() || "",
      title: data.title?.trim() || "",
      location: BRANCH_ID,
      reviewtext: data.comment.trim(),
      food: data.food?.toString() || "0",
      value: data.value?.toString() || "0",
      service: data.service?.toString() || "0",
      ambiance: data.ambiance?.toString() || "0",
    }

    // Call the external API to post the review
    const response = await fetch("https://tossdown.com/api/add_reviews", {
      method: "POST",
      headers: {
        "Content-Type": "application/x-www-form-urlencoded",
      },
      body: new URLSearchParams(reviewData).toString(),
    })

    const result = await response.json()

    if (!response.ok) {
      return NextResponse.json(
        {
          status: "error",
          message: "Failed to submit review to external API",
          details: result,
        },
        { status: 500 },
      )
    }

    return NextResponse.json({
      status: "success",
      message: "Review submitted successfully",
      data: result,
    })
  } catch (error) {
    console.error("Error submitting review:", error)
    return NextResponse.json(
      {
        status: "error",
        message: "An error occurred while submitting the review",
        error: error instanceof Error ? error.message : String(error),
      },
      { status: 500 },
    )
  }
}
