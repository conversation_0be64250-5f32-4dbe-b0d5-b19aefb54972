"use server"

import { submitProductReview } from "@/services/review-api"
import { revalidatePath } from "next/cache"
import { fetchProductDetail, mapProductDetailToViewModel } from "@/services/product-detail-api"
import { extractProductIdFromSlug } from "@/utils/product-utils"

// Add a new function to get product by slug
export async function getProductBySlug(slug: string) {
  try {
    const productId = extractProductIdFromSlug(slug)
    return await getProductById(productId.toString())
  } catch (error) {
    console.error("Error getting product by slug:", error)
    return null
  }
}

// Keep the existing function for backward compatibility
export async function getProductById(productId: string) {
  try {
    const productDetails = await fetchProductDetail(Number(productId))

    if (productDetails && productDetails.items && productDetails.items.length > 0) {
      return mapProductDetailToViewModel(productDetails)
    }

    return null
  } catch (error) {
    console.error("Error getting product by ID:", error)
    return null
  }
}

export async function submitReview(reviewData: {
  rating: number
  name: string
  comment: string
  productId: string
}) {
  try {
    await submitProductReview(reviewData)

    // Revalidate the product page to show the new review
    revalidatePath(`/product/${reviewData.productId}`)

    return { success: true }
  } catch (error) {
    console.error("Error submitting review:", error)
    return { success: false, error: "Failed to submit review" }
  }
}
