"use client"

import type React from "react"

import Link from "next/link"
import Image from "next/image"
import { Menu } from "lucide-react"
import { useCart } from "@/context/cart-context"
import { usePathname, useRouter } from "next/navigation"
import { useBusiness } from "@/context/business-context"
import { useState, useEffect, useRef } from "react"
import ShoppingCartIcon from "./icons/shopping-cart-icon"

// Import the auth context and modal wrapper
import { useAuth } from "@/context/auth-context"
import AuthModalWrapper from "./auth-modal-wrapper"
import { LOGO_URL } from "@/constants/app-constants"
import { showToast } from "@/utils/toast"

// Update the Header component to include auth state and modal
export default function Header() {
  // Wrap all context hooks in try-catch to prevent errors
  let totalItems = 0
  let orderType = "delivery"
  let setOrderType = () => {}
  let deliveryAddress = null
  let deliveryTiming = null
  let setDeliveryTiming = () => {}
  let scheduledDateTime = null
  let setScheduledDateTime = () => {}
  let orderSchedule = null
  let setOrderSchedule = () => {}

  const cartContext = useCart()
  totalItems = cartContext?.totalItems || 0
  orderType = cartContext?.orderType || "delivery"
  setOrderType = cartContext?.setOrderType || (() => {})
  deliveryAddress = cartContext?.deliveryAddress || null
  deliveryTiming = cartContext?.deliveryTiming || null
  setDeliveryTiming = cartContext?.setDeliveryTiming || (() => {})
  scheduledDateTime = cartContext?.scheduledDateTime || null
  setScheduledDateTime = cartContext?.setScheduledDateTime || (() => {})
  orderSchedule = cartContext?.orderSchedule || null
  setOrderSchedule = cartContext?.setOrderSchedule || (() => {})

  // Initialize business context values with defaults
  let businessLogo = ""
  let businessName = ""
  let selectedBranch = null

  // Call useBusiness hook unconditionally
  const businessContext = useBusiness()
  businessLogo = businessContext?.businessLogo || ""
  businessName = businessContext?.businessName || ""
  selectedBranch = businessContext?.selectedBranch || null

  // Initialize auth context values with defaults
  const authContext = useAuth()
  const user = authContext.user
  const isAuthenticated = authContext.isAuthenticated
  const logout = authContext.logout

  const pathname = usePathname()
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [authModalOpen, setAuthModalOpen] = useState(false)
  const [authModalView, setAuthModalView] = useState<"login" | "signup" | "forgot-password">("login")
  const router = useRouter()
  // First, add a state variable to track if the dropdown is open
  // Add this near the other useState declarations at the top of the component
  const [desktopProfileMenuOpen, setDesktopProfileMenuOpen] = useState(false)
  const [mobileProfileMenuOpen, setMobileProfileMenuOpen] = useState(false)
  const desktopProfileMenuRef = useRef<HTMLDivElement>(null)
  const mobileProfileMenuRef = useRef<HTMLDivElement>(null)
  // Add a new state variable to track if we're on a profile page
  const [isProfilePage, setIsProfilePage] = useState(false)

  // Check if we're on the checkout page
  const isCheckoutPage = pathname === "/checkout"

  // Function to toggle cart visibility
  const toggleCart = () => {
    try {
      // Create a custom event to toggle the cart
      const event = new CustomEvent("toggleCart")
      document.dispatchEvent(event)
    } catch (error) {
      console.error("Error toggling cart:", error)
    }
  }

  // Handle menu link click
  const handleMenuClick = (e: React.MouseEvent) => {
    try {
      e.preventDefault()

      // Always navigate to the our-menu page
      router.push("/our-menu")

      // Close mobile menu if open
      if (mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    } catch (error) {
      console.error("Error handling menu click:", error)
      router.push("/our-menu")
    }
  }

  // In the openLoginModal function, update to use the correct view
  const openLoginModal = () => {
    setAuthModalView("login")
    setAuthModalOpen(true)
  }

  // In the openSignupModal function, update to use the correct view
  const openSignupModal = () => {
    setAuthModalView("signup")
    setAuthModalOpen(true)
  }

  // Add a new function for opening the forgot password modal
  const openForgotPasswordModal = () => {
    setAuthModalView("forgot-password")
    setAuthModalOpen(true)
  }

  // Handle logout with proper event handling
  const handleLogout = (e: React.MouseEvent) => {
    try {
      e.preventDefault()
      e.stopPropagation()

      setDesktopProfileMenuOpen(false)
      setMobileProfileMenuOpen(false)

      // Add a small delay to ensure the dropdown closes first
      setTimeout(() => {
        logout()

        // Use Next.js router instead of window.location.href
        router.push("/")

        // Show a toast notification for better UX
        showToast({
          message: "You have been logged out successfully",
          type: "info",
          duration: 3000,
        })
      }, 10)
    } catch (error) {
      console.error("Error handling logout:", error)
      router.push("/")
    }
  }

  // Update the renderAuthButtons function to include the down arrow and click behavior
  const renderAuthButtons = () => {
    try {
      if (isAuthenticated) {
        // Don't show the profile dropdown button on profile pages
        if (isProfilePage) {
          return null
        }

        return (
          <div className="relative profile-menu-container" ref={desktopProfileMenuRef}>
            <button
              className="flex items-center justify-center gap-1"
              onClick={() => setDesktopProfileMenuOpen(!desktopProfileMenuOpen)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="currentColor"
                stroke="currentColor"
                strokeWidth="1"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                <circle cx="12" cy="7" r="4"></circle>
              </svg>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="m6 9 6 6 6-6" />
              </svg>
            </button>
            {desktopProfileMenuOpen && (
              <div className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg z-50">
                <div className="py-1">
                  <Link href="/profile" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    Personal Information
                  </Link>
                  <Link
                    href="/profile/subscriptions"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Subscriptions
                  </Link>
                  <Link href="/profile/orders" className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                    View Orders
                  </Link>
                  <Link
                    href="/profile/activity-log"
                    className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Activity Log
                  </Link>
                  <button
                    onClick={handleLogout}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Logout
                  </button>
                </div>
              </div>
            )}
          </div>
        )
      }

      return (
        <>
          <button onClick={openLoginModal} className="text-sm border border-black rounded-full px-2 hover:bg-gray-100">
            LOGIN
          </button>
          <button onClick={openSignupModal} className="text-sm border border-black rounded-full px-1 hover:bg-gray-100">
            SIGN UP
          </button>
        </>
      )
    } catch (error) {
      console.error("Error rendering auth buttons:", error)
      return (
        <>
          <button onClick={openLoginModal} className="text-sm border border-black rounded-full px-2 hover:bg-gray-100">
            LOGIN
          </button>
          <button onClick={openSignupModal} className="text-sm border border-black rounded-full px-1 hover:bg-gray-100">
            SIGN UP
          </button>
        </>
      )
    }
  }

  // Replace the renderMobileAuthOptions function with this updated version
  const renderMobileAuthOptions = () => {
    // No longer showing login/signup buttons in the mobile menu
    return null
  }

  // Add an effect to close the profile menus when clicking outside
  useEffect(() => {
    try {
      const handleClickOutside = (event: MouseEvent) => {
        if (
          desktopProfileMenuOpen &&
          desktopProfileMenuRef.current &&
          !desktopProfileMenuRef.current.contains(event.target as Node)
        ) {
          setDesktopProfileMenuOpen(false)
        }
        if (
          mobileProfileMenuOpen &&
          mobileProfileMenuRef.current &&
          !mobileProfileMenuRef.current.contains(event.target as Node)
        ) {
          setMobileProfileMenuOpen(false)
        }
      }

      document.addEventListener("mousedown", handleClickOutside)
      return () => {
        document.removeEventListener("mousedown", handleClickOutside)
      }
    } catch (error) {
      console.error("Error in profile menu click outside effect:", error)
    }
  }, [desktopProfileMenuOpen, mobileProfileMenuOpen])

  // Add this useEffect hook after the other useEffect hooks
  useEffect(() => {
    try {
      // Update the state whenever pathname changes
      setIsProfilePage(pathname?.startsWith("/profile") || false)

      // Close profile menus when navigating to a profile page
      if (pathname?.startsWith("/profile")) {
        setDesktopProfileMenuOpen(false)
        setMobileProfileMenuOpen(false)
      }
    } catch (error) {
      console.error("Error in pathname effect:", error)
    }
  }, [pathname])

  return (
    <>
      {/* Black bar with height of 42px */}
      <div className="bg-black h-[42px] w-full"></div>

      {isCheckoutPage ? (
        // Simplified header for checkout page
        <header className="bg-white border-b border-gray-200">
          <div className="max-w-[1220px] mx-auto px-4">
            <div className="flex items-center justify-between py-3 md:py-[21px]">
              {/* Logo - using the same image as home page */}
              <Link href="/" className="flex-shrink-0">
                <div className="relative">
                  <Image
                    src={LOGO_URL || "/placeholder.svg"}
                    alt={businessName || "EZeats"}
                    width={170}
                    height={59}
                    className="object-contain w-[84px] h-[29px] md:w-[170px] md:h-[59px]"
                    priority
                  />
                </div>
              </Link>

              {/* Checkout text with items count */}
              <div className="flex items-center">
                <span className="text-lg font-medium">
                  Checkout ({totalItems} {totalItems === 1 ? "Item" : "Items"})
                </span>
              </div>
            </div>
          </div>
        </header>
      ) : (
        // Regular header for other pages
        <header className={`${pathname === "/" ? "bg-[#F9CB01] md:bg-white" : "bg-white"} shadow-sm`}>
          <div className="container mx-auto px-4 max-w-6xl flex items-center justify-between py-3 md:py-[21px]">
            {/* Left section with logo and navigation */}
            <div className="flex items-center">
              {/* Logo */}
              <Link href="/" className="flex-shrink-0">
                <div className="relative">
                  <Image
                    src={LOGO_URL || "/placeholder.svg"}
                    alt={businessName || "EZeats"}
                    width={170}
                    height={59}
                    className="object-contain w-[84px] h-[29px] md:w-[170px] md:h-[59px]"
                    priority
                  />
                </div>
              </Link>

              {/* Desktop Navigation with 32px gap after logo */}
              <nav className="hidden md:flex items-center space-x-[32px] ml-[32px]">
                <Link href="/" className="text-sm font-medium hover:text-yellow-500">
                  HOME
                </Link>
                <a
                  href="#"
                  onClick={handleMenuClick}
                  className="text-sm font-medium hover:text-yellow-500 cursor-pointer"
                >
                  MENU
                </a>
                <Link href="/our-kitchen" className="text-sm font-medium hover:text-yellow-500">
                  OUR KITCHEN
                </Link>
                <Link href="/contact-us" className="text-sm font-medium hover:text-yellow-500">
                  CONTACT US
                </Link>
              </nav>
            </div>

            {/* Mobile Order Type Toggle */}
            {pathname === "/" && (
              <div className="flex md:hidden">
                <div className="bg-white rounded-full p-0.5 flex">
                  <button
                    className={`flex-1 py-1 px-2 text-xs font-medium text-center rounded-full transition-colors ${
                      orderType === "delivery" ? "bg-black text-white shadow-md" : ""
                    }`}
                    onClick={() => setOrderType("delivery")}
                  >
                    Delivery
                  </button>
                  <button
                    className={`flex-1 py-1 px-2 text-xs font-medium text-center rounded-full transition-colors ${
                      orderType === "pickup" ? "bg-black text-white shadow-md" : ""
                    }`}
                    onClick={() => setOrderType("pickup")}
                  >
                    Pickup
                  </button>
                </div>
              </div>
            )}

            {/* Login/Signup and Cart for desktop */}
            <div className="hidden md:flex items-center space-x-[9px]">
              {renderAuthButtons()}

              <button onClick={toggleCart} className="relative cursor-pointer" aria-label="Open cart" type="button">
                <ShoppingCartIcon className="w-6 h-6" />
                <span className="absolute -top-2 -right-2 bg-[#ffa800] text-white text-xs font-semibold text-center leading-[18px] rounded-full w-[18px] h-[18px] pl-[1px]">
                  {totalItems}
                </span>
              </button>
            </div>

            {/* Mobile Cart and Menu */}
            <div className="flex md:hidden items-center space-x-4">
              {/* Profile Icon - Only show if not on profile page */}
              {!isProfilePage && (
                <div className="relative profile-menu-container" ref={mobileProfileMenuRef}>
                  <button
                    onClick={() => {
                      if (isAuthenticated) {
                        setMobileProfileMenuOpen(!mobileProfileMenuOpen)
                      } else {
                        openLoginModal()
                      }
                    }}
                    className="relative cursor-pointer flex items-center"
                    aria-label="Profile"
                    type="button"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="currentColor"
                      stroke="currentColor"
                      strokeWidth="1"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                    {isAuthenticated && (
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="m6 9 6 6 6-6" />
                      </svg>
                    )}
                  </button>
                  {isAuthenticated && mobileProfileMenuOpen && (
                    <div
                      className="absolute right-0 mt-2 w-56 bg-white rounded-md shadow-lg z-[10000]"
                      style={{ maxWidth: "calc(100vw - 20px)" }}
                    >
                      <div className="py-1">
                        <Link
                          href="/profile"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setMobileProfileMenuOpen(false)}
                        >
                          Personal Information
                        </Link>
                        <Link
                          href="/profile/subscriptions"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setMobileProfileMenuOpen(false)}
                        >
                          Subscriptions
                        </Link>
                        <Link
                          href="/profile/orders"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setMobileProfileMenuOpen(false)}
                        >
                          View Orders
                        </Link>
                        <Link
                          href="/profile/activity-log"
                          className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                          onClick={() => setMobileProfileMenuOpen(false)}
                        >
                          Activity Log
                        </Link>
                        <button
                          onClick={handleLogout}
                          className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        >
                          Logout
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              )}

              {/* Cart button - always visible */}
              <button onClick={toggleCart} className="relative cursor-pointer" aria-label="Open cart" type="button">
                <ShoppingCartIcon className="w-5 h-5" />
                <span className="absolute -top-2 -right-2 bg-[#ffa800] text-white text-xs font-semibold text-center leading-[18px] rounded-full w-[18px] h-[18px] pl-[1px]">
                  {totalItems}
                </span>
              </button>

              {/* Menu button - always visible and explicitly positioned */}
              <button
                onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
                className="cursor-pointer z-[100]"
                aria-label="Toggle menu"
                type="button"
              >
                <Menu size={24} />
              </button>
            </div>

            {/* Mobile Menu */}
            {mobileMenuOpen && (
              <div
                className="fixed inset-0 bg-black bg-opacity-50 z-[9999] flex md:hidden"
                onClick={() => setMobileMenuOpen(false)}
              >
                <div className="bg-white w-4/5 max-w-xs h-full overflow-y-auto" onClick={(e) => e.stopPropagation()}>
                  <div className="p-4 flex justify-end items-center ">
                    <button onClick={() => setMobileMenuOpen(false)} className="text-black" aria-label="Close menu">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </button>
                  </div>
                  <nav className="flex flex-col mobile_menu">
                    <Link
                      href="/"
                      className="px-4 py-3 text-sm font-medium border-b border-gray-200"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      HOME
                    </Link>
                    <a
                      href="#"
                      onClick={handleMenuClick}
                      className="px-4 py-3 text-sm font-medium border-b border-gray-200"
                    >
                      MENU
                    </a>
                    <Link
                      href="/our-kitchen"
                      className="px-4 py-3 text-sm font-medium border-b border-gray-200"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      OUR KITCHEN
                    </Link>
                    <Link
                      href="/contact-us"
                      className="px-4 py-3 text-sm font-medium border-b border-gray-200"
                      onClick={() => setMobileMenuOpen(false)}
                    >
                      CONTACT US
                    </Link>
                    {renderMobileAuthOptions()}
                  </nav>
                </div>
              </div>
            )}
          </div>
        </header>
      )}

      {/* Auth Modal */}
      <AuthModalWrapper isOpen={authModalOpen} initialView={authModalView} onClose={() => setAuthModalOpen(false)} />
    </>
  )
}
