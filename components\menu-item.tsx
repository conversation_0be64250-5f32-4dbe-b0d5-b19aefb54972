"use client"

import { useCart } from "@/context/cart-context"
import Image from "next/image"
import { Star, Plus } from "lucide-react"

type MenuItemProps = {
  id: number
  name: string
  image: string
  rating: number
  price: number
  isVeg: boolean
}

export default function MenuItem({ id, name, image, rating, price, isVeg }: MenuItemProps) {
  const { addItem } = useCart()

  const handleAddToCart = () => {
    addItem({ id, name, image: image || "/placeholder.svg", price, isVeg })
  }

  return (
    <div className="bg-white rounded-lg overflow-hidden shadow-md relative">
      <div className="relative h-48">
        <div className="absolute top-2 left-2 bg-black text-white text-xs px-2 py-1 rounded z-10">
          {isVeg ? "Veg" : "Non-Veg"}
        </div>
        <Image src={image || "/placeholder.svg"} alt={name || "Menu item"} fill className="object-cover" />
      </div>
      <div className="p-4">
        <h3 className="font-medium text-lg mb-2 ">{name || "Unnamed item"}</h3>
        <div className="flex mb-2">
          {[...Array(5)].map((_, i) => (
            <Star
              key={i}
              size={16}
              className={i < (rating || 0) ? "fill-yellow-400 text-yellow-400" : "text-gray-300"}
            />
          ))}
        </div>
        <div className="flex justify-between items-center">
          <p className="font-medium">CAD {(price || 0).toFixed(2)}</p>
          <button onClick={handleAddToCart} className="bg-black text-white p-2 rounded-full hover:bg-gray-800">
            <Plus size={16} />
          </button>
        </div>
      </div>
    </div>
  )
}
