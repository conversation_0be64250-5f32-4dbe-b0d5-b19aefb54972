import type { Metadata } from "next"
import { getPageSeo } from "@/services/seo-service"

export async function generateMetadata(): Promise<Metadata> {
  // Get SEO data for the "contact" page from XML
  const pageSeo = await getPageSeo("contact")

  // Use SEO data if available, otherwise fall back to defaults
  return {
    title: pageSeo?.seo?.pagetitle || "Contact Us - EZeats Canada",
    description:
      pageSeo?.seo?.desc ||
      "Get in touch with EZeats Canada for Tiffin and Food Delivery Services. We're here to answer your questions and provide assistance.",
    keywords:
      pageSeo?.seo?.keywords || "contact EZeats, food delivery contact, tiffin service contact, EZeats Canada contact",
  }
}
