import type { Metada<PERSON> } from "next"
import { generatePageMetadata } from "@/lib/metadata-utils"
import { getPageSeo } from "@/services/seo-service"
import { fetchProducts, mapProductToMenuItem, filterValidProducts } from "@/services/api"
import MenuContent from "@/components/menu-content"

export const revalidate = 3600 // Revalidate every hour

export async function generateMetadata(): Promise<Metadata> {
  // Use "menu" XML page name for the our-menu page
  return generatePageMetadata("menu")
}

export default async function OurMenuPage() {
  let menuItems = []
  let error = null

  // Get the H1 from XML (using "menu" as the XML page name)
  const pageSeo = await getPageSeo("menu")
  const h1 = pageSeo?.seo?.h1 || null

  try {
    const data = await fetchProducts()
    // Filter valid products before mapping
    const validProducts = filterValidProducts(data.items)
    menuItems = validProducts.map(mapProductToMenuItem)
  } catch (err) {
    error = "Failed to load menu items. Please try again later."
    console.error(error, err)
  }

  return (
    <>
      {/* Use H1 from XML if available */}
      {h1 ? (
        <h1 className="sr-only">{h1}</h1>
      ) : (
        <h1 className="sr-only">Indian Vegetarian Dishes and Meal Kits in Canada</h1>
      )}
      <MenuContent menuItems={menuItems} error={error} />
    </>
  )
}
