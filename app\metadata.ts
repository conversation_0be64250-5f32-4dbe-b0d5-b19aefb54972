import type { Metadata } from "next"
import { getPageSeo } from "@/services/seo-service"

export async function generateMetadata(): Promise<Metadata> {
  const pageSeo = await getPageSeo("home")

  return {
    title: pageSeo?.seo?.pagetitle || "EZeats Canada - Fresh Meal Delivery and Tiffin Service",
    description: pageSeo?.seo?.desc || "Order fresh, delicious meals delivered to your doorstep.",
    keywords: pageSeo?.seo?.keywords || "meal delivery, tiffin service, food delivery",
  }
}
