"use client"

import { createContext, useContext, type <PERSON>actN<PERSON>, useState, useEffect } from "react"

interface SeoData {
  pagetitle: string
  h1: string
  keywords: string
  desc: string
}

interface PageSeo {
  seo: SeoData
  generated: SeoData
}

interface SeoContextType {
  seoData: Record<string, PageSeo> | null
  isLoading: boolean
  error: Error | null
}

const SeoContext = createContext<SeoContextType>({
  seoData: null,
  isLoading: true,
  error: null,
})

export function SeoProvider({ children }: { children: ReactNode }) {
  const [seoData, setSeoData] = useState<Record<string, PageSeo> | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    async function loadSeoData() {
      try {
        const response = await fetch("/api/seo")

        if (!response.ok) {
          throw new Error("Failed to fetch SEO data")
        }

        const data = await response.json()
        setSeoData(data)
      } catch (err) {
        console.error("Error loading SEO data:", err)
        setError(err instanceof Error ? err : new Error("Failed to load SEO data"))
      } finally {
        setIsLoading(false)
      }
    }

    loadSeoData()
  }, [])

  return <SeoContext.Provider value={{ seoData, isLoading, error }}>{children}</SeoContext.Provider>
}

export function useSeo() {
  return useContext(SeoContext)
}
