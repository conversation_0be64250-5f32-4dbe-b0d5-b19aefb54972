"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { X, Eye, EyeOff } from "lucide-react"
import { useToast } from "@/context/toast-context"
// Add the import for our auth utilities
import { setUserAuthResponse } from "@/utils/auth-utils"
import { BUSINESS_ID } from "@/constants/app-constants"
// Import the useRouter hook at the top
import { useRouter } from "next/navigation"
import { useAuth } from "@/context/auth-context"

type SignupModalProps = {
  isOpen: boolean
  onClose: () => void
  onSwitchToLogin: () => void
}

// Add router and auth context to the component
export default function SignupModal({ isOpen, onClose, onSwitchToLogin }: SignupModalProps) {
  const [name, setName] = useState("")
  const [email, setEmail] = useState("")
  const [phone, setPhone] = useState("") // Add phone state
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const modalRef = useRef<HTMLDivElement>(null)
  const { showToast } = useToast()
  const router = useRouter()
  const { refreshAuthState } = useAuth()

  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onClose])

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey)
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey)
    }
  }, [isOpen, onClose])

  // Update the handleSubmit function to use Next.js routing
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      // Format phone number if needed (ensure it has country code)
      const formattedPhone = phone.startsWith("+") ? phone : `+${phone}`

      // Call the signup API
      const response = await fetch(
        `https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/user/signup`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            name,
            email,
            phone: formattedPhone,
            password,
            address: "",
            city: "",
            gender: "",
            dob: "",
            token: "",
            source: "web",
            device: "web",
            facebook_id: "",
            google_id: "",
            apple_id: "",
          }),
        },
      )

      const data = await response.json()

      if (response.ok && data.status === 200) {
        // Store user data in cookies using our utility function
        setUserAuthResponse({}, data.result)

        // Refresh the auth state in context
        refreshAuthState()

        // Show success message
        showToast({
          message: "Account created successfully!",
          type: "success",
          duration: 3000,
        })

        // Close the modal
        onClose()

        // Check if we're not on the home page, and navigate if needed
        if (window.location.pathname !== "/") {
          router.push("/")
        }
      } else {
        // Show error message
        showToast({
          message: data.message || "Signup failed. Please try again.",
          type: "error",
          duration: 4000,
        })
      }
    } catch (error) {
      console.error("Signup error:", error)
      showToast({
        message: "An error occurred during signup. Please try again.",
        type: "error",
        duration: 4000,
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div
        ref={modalRef}
        className="bg-white rounded-lg w-full max-w-md overflow-hidden font-poppins"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-anton uppercase tracking-wide">Create an account</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={24} />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex mb-6 bg-gray-100 rounded-full p-1">
            <button
              onClick={onSwitchToLogin}
              className="flex-1 py-2 px-4 rounded-full text-gray-600 font-medium text-center hover:bg-gray-200"
            >
              Login
            </button>
            <button className="flex-1 py-2 px-4 rounded-full bg-white font-medium text-center shadow">Sign up</button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="name" className="block text-sm font-medium text-gray-600 mb-1">
                Full Name
              </label>
              <input
                type="text"
                id="name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md font-poppins"
                placeholder="Enter your full name"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="signup-email" className="block text-sm font-medium text-gray-600 mb-1">
                Email address
              </label>
              <input
                type="email"
                id="signup-email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md font-poppins"
                placeholder="Enter your email"
                required
              />
            </div>

            <div className="mb-4">
              <label htmlFor="phone" className="block text-sm font-medium text-gray-600 mb-1">
                Phone Number
              </label>
              <input
                type="tel"
                id="phone"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md font-poppins"
                placeholder="Enter your phone number"
                required
              />
            </div>

            <div className="mb-6">
              <label htmlFor="signup-password" className="block text-sm font-medium text-gray-600 mb-1">
                Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="signup-password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md pr-10 font-poppins"
                  placeholder="Create a password"
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 bg-black text-white rounded-md font-medium hover:bg-gray-900 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
            >
              {isLoading ? "Creating account..." : "Sign up"}
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}
