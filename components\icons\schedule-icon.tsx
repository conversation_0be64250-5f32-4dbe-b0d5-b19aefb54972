export default function ScheduleIcon({ className = "", size = 24 }: { className?: string; size?: number }) {
  return (
    <svg
      version="1.1"
      width={size}
      height={size}
      viewBox="0 0 32 32"
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <path
        d="M7,7V3c0-0.551,0.449-1,1-1s1,0.449,1,1v4c0,0.551-0.449,1-1,1S7,7.551,7,7z M30,8v16
        c0,1.657-1.343,3-3,3H5c-1.657,0-3-1.343-3-3V8c0-1.657,1.343-3,3-3h1v2c0,1.105,0.895,2,2,2c1.105,0,2-0.895,2-2V5h12v2
        c0,1.105,0.895,2,2,2c1.105,0,2-0.895,2-2V5h1C28.657,5,30,6.343,30,8z M14,16c0-0.552-0.448-1-1-1h-0.5c-0.276,0-0.5,0.224-0.5,0.5
        c0,0.276,0.224,0.5,0.5,0.5H13v6.5c0,0.276,0.224,0.5,0.5,0.5s0.5-0.224,0.5-0.5V16z M19,16c0-0.552-0.448-1-1-1h-0.5
        c-0.276,0-0.5,0.224-0.5,0.5c0,0.276,0.224,0.5,0.5,0.5H18v6.5c0,0.276,0.224,0.5,0.5,0.5s0.5-0.224,0.5-0.5V16z M29,11.5
        c0-0.276-0.224-0.5-0.5-0.5h-25C3.224,11,3,11.224,3,11.5S3.224,12,3.5,12h25C28.776,12,29,11.776,29,11.5z M24,8
        c0.551,0,1-0.449,1-1V3c0-0.551-0.449-1-1-1s-1,0.449-1,1v4C23,7.551,23.449,8,24,8z M5,28c-1.2,0-2.266-0.542-3-1.382V27
        c0,1.657,1.343,3,3,3h22c1.657,0,3-1.343,3-3v-0.382C29.266,27.458,28.2,28,27,28H5z"
        fill="currentColor"
      />
    </svg>
  )
}
