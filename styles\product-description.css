.log_des_data_parent {
  display: inline-block;
  width: 100%;
  padding: 0px 0px;
}

.custom_container {
  max-width: 1140px;
  margin: 0 auto;
}

.log_des_acc_main {
  display: block;
  width: 100%;
  margin-bottom: 30px;
}

.log_des_acc_header {
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-bottom: 30px;
  z-index: 999;
  border-bottom: none;
}

.log_des_acc_header:is(.collapsed) {
  padding-bottom: 0px;
}

.log_des_acc_header span.title {
  letter-spacing: 0em;
  text-transform: uppercase;
  font-family: Poppins;
  font-size: 36px;
  font-weight: 500;
  line-height: 54px;
  text-align: left;
  color: #585858;
  padding-right: 15px;
}

.log_des_acc_header .acc_icon i {
  font-size: 16px;
}

.log_des_acc_header .rotate-icon {
  transition: transform 0.3s ease;
}

.log_des_acc_header:not(.collapsed) .rotate-icon {
  transform: rotate(180deg);
  font-size: 30px;
  font-weight: 200;
  display: flex;
  align-items: center;
  justify-content: center;
}

em.fas.fa-angle-down.rotate-icon {
  color: #686868;
  font-size: 18px;
}

.log_des_toggle_data {
  transition: all 0.3s ease;
  display: grid;
  gap: 20px;
  width: 100%;
}

.log_des_toggle_data.hidden {
  display: none;
}

.log_des_toggle_data.block {
  display: block;
}

.log_des_toggle_data p {
  font-family: Poppins;
  font-size: 20px;
  font-weight: 400;
  line-height: 30px;
  text-align: left;
  color: #767676;
}

.log_des_toggle_data figure {
  display: grid;
  grid-template-columns: 1fr 10fr;
  align-items: center;
  gap: 17px;
}

.log_des_toggle_data figure img {
  object-fit: cover;
  width: 100%;
}

ul.log_des_toggle_data_listing {
  display: grid;
  width: 100%;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px 24px;
  padding: 0px;
  margin: 0px;
}

ul.log_des_toggle_data_listing li {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  color: #000;
  list-style: none;
  display: flex;
  align-items: center;
}

ul.log_des_toggle_data_listing li i {
  color: #000;
  font-size: 8px;
  padding-right: 10px;
}

ul.log_des_toggle_data_listing_two {
  display: grid;
  width: 100%;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 24px 24px;
  padding: 0px;
  margin: 0px;
}

ul.log_des_toggle_data_listing_two li {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  color: #000;
  list-style: none;
  display: flex;
  align-items: center;
}

ul.log_des_toggle_data_listing_two li span {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 700;
  line-height: 18px;
  letter-spacing: 0em;
  margin-right: 10px;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #000;
  color: #fff;
  min-width: 30px;
}

.log_des_rating_box {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.log_des_rating_num {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-bottom: 15px;
  margin-bottom: 32px;
}

.log_des_rating_num h3 {
  font-family: Anton;
  font-size: 24px;
  font-weight: 400;
  line-height: 36px;
  letter-spacing: 0em;
  text-transform: uppercase;
  color: #000;
  padding-bottom: 20px;
  margin: 0px;
}

.log_des_rating_num small {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  text-transform: capitalize;
}

.log_des_rating_num_detail {
  display: flex;
  align-items: center;
  width: 100%;
}

.log_des_rating_num_detail h2 {
  font-family: Poppins;
  font-size: 64px;
  font-weight: 600;
  line-height: 96px;
  letter-spacing: 0em;
  padding-right: 18px;
  margin: 0px;
}

.log_des_total_rating {
  display: flex;
  flex-direction: column;
}

.log_des_total_rating_stars {
  display: flex;
}

.log_des_total_rating_stars i {
  padding-right: 4px;
  font-size: 20px;
  color: #ffa800;
}

.log_des_total_rating p {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  margin: 0px;
}

.log_des_review_list_main {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.log_des_review_list_main h3 {
  padding-bottom: 20px;
  margin: 0px;
  letter-spacing: 0em;
  text-transform: uppercase;
  font-family: Poppins;
  font-size: 36px;
  font-weight: 500;
  line-height: 54px;
  text-align: left;
  color: #585858;
  padding-right: 15px;
}

.log_des_review_list {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.log_des_single_review {
  border-bottom: 1px solid #00000026;
  padding-bottom: 24px;
  margin-bottom: 24px;
  display: grid;
  width: 100%;
  grid-template-columns: 1fr 3fr;
  gap: 20px 30px;
}

.log_des_single_review figure {
  display: flex;
  align-items: flex-start;
  margin: 0px;
}

.log_des_single_review figure img {
  margin-right: 10px;
  width: 48px;
  height: 48px;
  object-fit: cover;
}

.log_des_single_review figure figcaption {
  display: flex;
  flex-direction: column;
}

.log_des_single_review figure figcaption h5 {
  font-family: Poppins;
  font-size: 15px;
  font-weight: 600;
  line-height: 23px;
  letter-spacing: 0em;
  color: #212121;
  margin: 0px;
}

.log_des_single_review figure figcaption span {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  letter-spacing: 0em;
  color: #9fa19d;
}

.log_des_single_review_data {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.log_des_single_review_stars {
  display: flex;
  padding-bottom: 8px;
}

.log_des_single_review_stars i {
  padding-right: 4px;
  font-size: 16px;
  color: #ffa800;
}

.log_des_single_review_data p {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0em;
  color: #464646;
  margin: 0px;
}
 .detail_page_long_des_table{

    display: grid;
    grid-template-columns: 1fr;
    width: 100%;
    max-width: 900px;
    margin: 0 auto;
    border: 1px solid #6E6E6E;
    border-radius: 52px;
  }
.detail_page_long_des_table_heading_main h2 {
    font-family: Poppins;
    font-size: 52px;
    font-weight: 500 !important;
    line-height: 77.99px;
    letter-spacing: 0.35em;
    text-align: center;
    color: #585858;
    margin: 0px;
    border-bottom: 1px solid #A3A3A3;
}
  .detail_page_long_des_table_heading {
display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    padding: 0px 50px;
  }
  
  .detail_page_long_des_table_heading h2{
    background: #ffffff;
    font-style: normal;
    color: #767676;
    padding: 15px 5px;
    margin: 0px;
    word-break: break-all;
    height: 100%;
    align-items: flex-end;
    display: flex;
    font-family: Poppins;
    font-size: 20px;
    font-weight: 500 !important;
    line-height: 23.4px;
    text-align: right;
    justify-content: flex-end;
    flex-direction: column;
  }

  
  .detail_page_long_des_table_detail{
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    padding: 0px 50px;
    border-bottom: 1px solid #A3A3A3;
  }
.detail_page_long_des_table_detail:last-child {
    border: unset;
}
  .detail_page_long_des_table_detail span{
    color: #767676;
    margin: 0px;
    padding: 12px 5px;
    height: 100%;
    align-items: center;
    display: flex;
    word-break: break-all;
    font-family: Poppins;
    font-size: 20px;
    font-weight: 400;
    line-height: 44px;
    text-align: right;
    justify-content: flex-end;
  }
  .detail_page_long_des_table_detail span:first-child{
    justify-content: flex-start;
    text-align: left;
  }

@media (max-width: 540px) {
  .log_des_acc_header span.title {
    font-size: 18px;
    line-height: 24px;
  }
  .log_des_acc_main {
    margin-bottom: 20px;
  }
  ul.log_des_toggle_data_listing {
    grid-template-columns: 1fr 1fr;
    gap: 20px 20px;
  }
  ul.log_des_toggle_data_listing_two {
    grid-template-columns: 1fr;
    gap: 20px 20px;
  }
  ul.log_des_toggle_data_listing_two li span {
    width: 25px;
    height: 25px;
    min-width: 25px;
  }
  .log_des_rating_num h3 {
    font-size: 18px;
    line-height: 24px;
    padding-bottom: 15px;
  }
  .log_des_rating_num_detail h2 {
    font-size: 44px;
    line-height: 70px;
    padding-right: 15px;
  }
  .log_des_total_rating_stars i {
    font-size: 16px;
  }
  .log_des_total_rating p {
    font-size: 12px;
  }
  .log_des_rating_num {
    padding-bottom: 10px;
    margin-bottom: 20px;
  }
  .log_des_review_list_main h3 {
    font-size: 18px;
    line-height: 24px;
    padding-bottom: 15px;
  }
}

@media (max-width: 768px) {
  .log_des_single_review {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1024px) {
  .product_detail_page_heading h2 {
    font-size: 20px;
    line-height: 34px;
  }
  .product_detail_page_add_to_cart_btn {
    padding-left: 10px;
  }
  .detail_page_long_des_table_heading_main h2 {
    font-size: 24px;
    line-height: 54.99px;
  }
  .detail_page_long_des_table_heading h2 {
    font-size: 16px;
  }
  .detail_page_long_des_table_heading {
    padding: 0px 15px;
  }
  .detail_page_long_des_table_detail {
    padding: 0px 15px;
  }
  .detail_page_long_des_table_detail span {
    font-size: 16px;
  }
  .log_des_toggle_data p {
    font-size: 16px;
    line-height: 24px;
  }
  .log_des_toggle_data figure {
    grid-template-columns: 1fr 12fr;
  }
  .log_des_acc_header span.title {
    font-size: 26px !important;
    line-height: 36px !important;
  }
}

@media (max-width: 540px) {
  .detail_page_long_des_table_detail {
    padding: 0px 10px;
  }
  .detail_page_long_des_table_heading {
    padding: 0px 10px;
  }
  .detail_page_long_des_table_heading_main h2 {
    font-size: 16px;
    line-height: 40.99px;
  }
  .detail_page_long_des_table_heading h2 {
    font-size: 12px;
    line-height: 17px;
  }
  .detail_page_long_des_table_detail span {
    font-size: 12px;
    line-height: 26px;
  }
  .detail_page_long_des_table {
    border-radius: 24px;
  }
  .log_des_data_parent {
    padding: 0px 0px;
  }
  .log_des_toggle_data p {
    font-size: 14px;
    line-height: 21px;
  }
  .log_des_acc_header span.title {
    font-size: 18px !important;
    line-height: 29px !important;
  }
  .log_des_toggle_data {
    gap: 12px;
  }
}

@media (max-width: 370px) {
  .detail_page_long_des_table_heading h2 {
    font-size: 10px;
    line-height: 17px;
  }
  .detail_page_long_des_table_detail span {
    font-size: 10px;
    line-height: 17px;
  }
}

.fixed_btn {
  position: fixed;
  bottom: 20px;
  z-index: 999;
  right: 20px;
}

.fixed_btn a {
  font-family: Poppins;
  font-size: 23px;
  font-weight: 400;
  line-height: 23px;
  letter-spacing: 0em;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 208px;
  height: 52px;
  border-radius: 2px;
  text-transform: capitalize;
  background: #000;
  color: #fff !important;
  position: relative;
  cursor: pointer;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
}

@media (max-width: 1024px) {
  .fixed_btn a {
    font-size: 18px;
    width: 155px;
    height: 42px;
  }
}

@media (max-width: 768px) {
  .fixed_btn a {
    height: 43px;
    width: 148px;
  }
}

@media (max-width: 540px) {
  .fixed_btn a {
    width: 132px;
    height: 40px;
    font-size: 16px;
  }
}
