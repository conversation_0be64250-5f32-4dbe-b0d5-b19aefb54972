import type { Metadata, ResolvingMetadata } from "next"
import { getProductSeo } from "@/services/seo-service"
import { getProductById } from "@/app/actions/product-actions"
import { extractProductIdFromSlug } from "@/utils/product-utils"

type Props = {
  params: { slug: string } // Changed from productId to slug
}

export async function generateMetadata({ params }: Props, parent: ResolvingMetadata): Promise<Metadata> {
  // Extract the product ID from the slug
  const productId = extractProductIdFromSlug(params.slug)

  // Get the product data
  const product = await getProductById(productId.toString())

  if (!product) {
    return {
      title: "Product Not Found - EZeats Canada",
      description: "The requested product could not be found.",
    }
  }

  // Get SEO data with product name placeholders replaced
  const productSeo = await getProductSeo(product.name)

  return {
    title: productSeo?.pagetitle || `${product.name} - EZeats Canada`,
    description: productSeo?.desc || `Order ${product.name} from EZeats for delivery to your doorstep.`,
    keywords: productSeo?.keywords || `${product.name}, meal delivery, tiffin service`,
  }
}
