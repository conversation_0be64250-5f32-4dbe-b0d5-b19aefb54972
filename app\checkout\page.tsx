"use client"

import { useEffect, useState, useRef } from "react"
import { useCart } from "@/context/cart-context"
import { useRouter } from "next/navigation"
import { getUniqueOrderId } from "@/services/cart-api"
import { Loader2 } from "lucide-react"
import { useBusiness } from "@/context/business-context"
import type { OrderSchedule, CartItem, BoxSize, AddressDetails, UserLocation } from "@/context/cart-context"

export default function CheckoutPage() {
  // Update the destructuring of cart context to include the new properties
  const { items, totalPrice, boxSize, orderType, deliveryTiming, addressDetails, userLocation, orderSchedule } =
    useCart()
  const { selectedBranch } = useBusiness()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(true)
  const [scriptError, setScriptError] = useState<string | null>(null)
  const [isCleaningUp, setIsCleaningUp] = useState(false)
  const mountedRef = useRef(false)
  const scriptsLoadedRef = useRef(false)
  const [cartChecked, setCartChecked] = useState(false)
  const redirectAttemptedRef = useRef(false)

  // Generate a unique key for remounting the component
  const pageKey = useRef(`checkout-${Date.now()}`).current

  // Reset the checkout application
  const resetCheckout = () => {
    // Clear any existing checkout app elements
    const rootElement = document.getElementById("root")
    if (rootElement) {
      rootElement.innerHTML = ""
    }

    // Reset scripts loaded flag
    scriptsLoadedRef.current = false

    // Remove any previously added scripts
    const oldScripts = document.querySelectorAll("script[data-checkout-script]")
    oldScripts.forEach((script) => script.remove())
  }

  // Cleanup function to remove all checkout-related elements
  const cleanupCheckout = () => {
    if (isCleaningUp) return
    setIsCleaningUp(true)

    // Remove any toast elements that might have been added by the checkout app
    const toastElements = document.querySelectorAll(
      '[class*="toast"], [id*="toast"], [class*="notification"], [id*="notification"]',
    )
    toastElements.forEach((el) => el.remove())

    // Remove any modal or overlay elements
    const overlayElements = document.querySelectorAll(
      '[class*="modal"], [id*="modal"], [class*="overlay"], [id*="overlay"], [class*="backdrop"], [id*="backdrop"]',
    )
    overlayElements.forEach((el) => el.remove())

    // Remove any checkout-specific elements that might have been added to the body
    const checkoutElements = document.querySelectorAll(
      '[class*="checkout"], [id*="checkout"], [class*="ordrz"], [id*="ordrz"]',
    )
    checkoutElements.forEach((el) => el.remove())

    // Remove any scripts added by the checkout app
    const checkoutScripts = document.querySelectorAll("script[data-checkout-script]")
    checkoutScripts.forEach((script) => script.remove())

    // Clear the root element
    const rootElement = document.getElementById("root")
    if (rootElement) {
      rootElement.innerHTML = ""
    }

    // Reset the businessInfo object
    if (window.businessInfo) {
      window.businessInfo = {} as any
    }

    // Remove any event listeners added by the checkout app (as much as possible)
    const cleanup = () => {
      try {
        // This is a best effort to remove event listeners
        const oldAddEventListener = window.addEventListener
        const oldRemoveEventListener = window.removeEventListener

        // Restore original methods if they were overridden
        if (window.addEventListener !== oldAddEventListener) {
          window.addEventListener = oldAddEventListener
        }

        if (window.removeEventListener !== oldRemoveEventListener) {
          window.removeEventListener = oldRemoveEventListener
        }
      } catch (e) {
        console.error("Error cleaning up event listeners:", e)
      }
    }

    cleanup()
    setIsCleaningUp(false)
  }

  // Get data directly from localStorage
  const getLocalStorageData = () => {
    // Default values
    const data = {
      cartItems: [] as CartItem[],
      boxSize: null as BoxSize,
      orderType: "delivery" as "delivery" | "pickup",
      deliveryTiming: "now",
      addressDetails: {
        address: "",
        city: "",
        appartment: "",
        area: "",
        country: "",
        postalCode: "",
      } as AddressDetails,
      userLocation: {
        lat: "",
        lng: "",
      } as UserLocation,
      orderSchedule: null as OrderSchedule | null,
    }

    try {
      // Get cart items
      const savedCart = localStorage.getItem("cart")
      if (savedCart) {
        data.cartItems = JSON.parse(savedCart)
      }

      // Get box size
      const savedBoxSize = localStorage.getItem("boxSize")
      if (savedBoxSize) {
        data.boxSize = JSON.parse(savedBoxSize)
      }

      // Get order type
      const savedOrderType = localStorage.getItem("orderType")
      if (savedOrderType) {
        data.orderType = JSON.parse(savedOrderType)
      }

      // Get delivery timing
      const savedTiming = localStorage.getItem("ezeats-delivery-timing")
      if (savedTiming) {
        data.deliveryTiming = savedTiming
      }

      // Get address details
      const savedAddressDetails = localStorage.getItem("addressDetails")
      if (savedAddressDetails) {
        data.addressDetails = JSON.parse(savedAddressDetails)
      }

      // Get user location
      const savedUserLocation = localStorage.getItem("userLocation")
      if (savedUserLocation) {
        data.userLocation = JSON.parse(savedUserLocation)
      }

      // Get order schedule
      const savedOrderSchedule = localStorage.getItem("orderSchedule")
      if (savedOrderSchedule) {
        data.orderSchedule = JSON.parse(savedOrderSchedule)
      }
    } catch (e) {
      console.error("Error reading data from localStorage:", e)
    }

    return data
  }

  // Check if cart data is available from localStorage directly
  const checkLocalStorageCart = () => {
    try {
      const savedCart = localStorage.getItem("cart")
      const savedBoxSize = localStorage.getItem("boxSize")

      if (savedCart) {
        const parsedCart = JSON.parse(savedCart)
        if (Array.isArray(parsedCart) && parsedCart.length > 0 && savedBoxSize) {
          return true
        }
      }
    } catch (e) {
      console.error("Error checking localStorage cart:", e)
    }
    return false
  }

  // Calculate total price from cart items
  const calculateTotalPrice = (cartItems: CartItem[]) => {
    return cartItems.reduce((total, item) => total + item.price * item.quantity, 0)
  }

  // Update the initializeCheckout function to better handle orderSchedule
  const initializeCheckout = () => {
    // Get data directly from localStorage
    const localData = getLocalStorageData()

    // If cart is empty or no box size selected, redirect back to menu
    if (localData.cartItems.length === 0 || !localData.boxSize) {
      // Double check with context values as a fallback
      if (items.length === 0 && !boxSize && !redirectAttemptedRef.current) {
        redirectAttemptedRef.current = true
        console.log("Cart is empty or no box size selected, redirecting to menu")
        router.push("/menu")
        return
      }
    }

    // Reset any previous checkout state
    resetCheckout()

    // Set loading state
    setIsLoading(true)
    setScriptError(null)

    // Get the same cart ID that's used in the cart API
    const cartId = getUniqueOrderId()

    // Calculate total price from localStorage cart items
    const calculatedTotalPrice = calculateTotalPrice(localData.cartItems)

    // Use context values as fallbacks
    const finalCartItems = localData.cartItems.length > 0 ? localData.cartItems : items
    const finalBoxSize = localData.boxSize || boxSize
    const finalOrderType = localData.orderType || orderType
    const finalDeliveryTiming = localData.deliveryTiming || deliveryTiming
    const finalAddressDetails = localData.addressDetails.address ? localData.addressDetails : addressDetails
    const finalUserLocation = localData.userLocation.lat ? localData.userLocation : userLocation
    const finalOrderSchedule = localData.orderSchedule || orderSchedule
    const finalTotalPrice = calculatedTotalPrice > 0 ? calculatedTotalPrice : totalPrice

    // Format cart data for the external checkout system
    const businessInfo = {
      cartId: cartId,
      businessId: "12536",
      orderType: finalOrderType,
      userLocation: {
        lat: finalUserLocation.lat || selectedBranch?.lat || "0",
        lng: finalUserLocation.lng || selectedBranch?.lng || "0",
      },
      theme: null,
      branchId: selectedBranch?.id.toString() || "18784",
      websiteLink: "https://ezeats.ca",
      subscriptionType:
        finalDeliveryTiming === "weekly"
          ? "weekly"
          : finalDeliveryTiming === "biweekly"
            ? "bi-weekly"
            : finalDeliveryTiming === "later"
              ? "schedule"
              : "",
      authType: "",
      addressDetails: {
        address: finalAddressDetails.address || "",
        city: finalAddressDetails.city || "",
        appartment: finalAddressDetails.appartment || "",
        area: finalAddressDetails.area || "",
        country: finalAddressDetails.country || "",
        postalCode: finalAddressDetails.postalCode || "",
      },
      orderSchedule: finalOrderSchedule || { date: "", time: "" },
      source: "ordrz",
      updateAddressFlag: true,
      // Add the missing properties required by the type definition
      items: finalCartItems.map((item) => ({
        id: item.id.toString(),
        name: item.name,
        price: item.price.toString(),
        qty: item.quantity,
        image: item.image,
        category_name: item.isVeg ? "Veg" : "Non-Veg",
      })),
      total_price: finalTotalPrice,
      box_size: finalBoxSize || null,
    }

    // Make the checkout data available to the external checkout application
    window.businessInfo = businessInfo

    // Load external scripts programmatically
    if (!scriptsLoadedRef.current) {
      const loadScript = (src: string) => {
        return new Promise<void>((resolve, reject) => {
          const script = document.createElement("script")
          script.src = src
          script.async = true
          script.defer = true
          script.setAttribute("data-checkout-script", "true")

          script.onload = () => {
            resolve()
          }

          script.onerror = (error) => {
            console.error(`Error loading script: ${src}`, error)
            reject(new Error(`Failed to load script: ${src}`))
          }

          document.body.appendChild(script)
        })
      }

      // Load scripts in sequence
      Promise.all([
        loadScript("https://checkout.ordrz.com/static/js/main.b75be690.js"),
        loadScript("https://checkout.ordrz.com/static/js/179.c3afeb96.chunk.js"),
      ])
        .then(() => {
          scriptsLoadedRef.current = true
          setIsLoading(false)
        })
        .catch((error) => {
          console.error("Failed to load checkout scripts:", error)
          setScriptError("Failed to load checkout application. Please try refreshing the page")
          setIsLoading(false)
        })
    } else {
      // Scripts already loaded, just hide loading
      setIsLoading(false)
    }
  }

  // Initialize on first mount and when dependencies change
  useEffect(() => {
    // Mark component as mounted
    mountedRef.current = true

    // Initialize checkout
    initializeCheckout()

    // Cleanup function
    return () => {
      mountedRef.current = false
      // Clean up checkout elements when component unmounts
      cleanupCheckout()
    }
  }, []) // Empty dependency array - only run on mount

  // Add a separate effect to check cart data after it's loaded
  useEffect(() => {
    // Wait a bit to ensure cart context is loaded
    const timer = setTimeout(() => {
      setCartChecked(true)
    }, 1000) // Wait 1 second for cart data to load

    return () => clearTimeout(timer)
  }, [])

  // Add event listener for page visibility changes
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Only reinitialize if the checkout is in a broken state
      if (document.visibilityState === "visible" && mountedRef.current) {
        // Check if the root element is empty or the checkout app is not properly loaded
        const rootElement = document.getElementById("root")
        if (!rootElement || !rootElement.children.length || !scriptsLoadedRef.current) {
          console.log("Checkout appears to be in a broken state, reinitializing...")
          initializeCheckout()
        }
      }
    }

    document.addEventListener("visibilitychange", handleVisibilityChange)

    return () => {
      document.removeEventListener("visibilitychange", handleVisibilityChange)
    }
  }, [])

  // Add event listener for navigation
  useEffect(() => {
    // Listen for navigation events
    const handleBeforeUnload = () => {
      cleanupCheckout()
    }

    // Add event listener for page unload
    window.addEventListener("beforeunload", handleBeforeUnload)

    // Clean up on component unmount
    return () => {
      window.removeEventListener("beforeunload", handleBeforeUnload)
      cleanupCheckout()
    }
  }, [])

  // Add a cleanup effect that runs when the component unmounts
  useEffect(() => {
    return () => {
      cleanupCheckout()
    }
  }, [])

  // Add a separate effect to handle cart changes
  useEffect(() => {
    // Only update if already mounted and scripts are loaded
    if (mountedRef.current && scriptsLoadedRef.current && cartChecked) {
      // Get data directly from localStorage
      const localData = getLocalStorageData()

      // Calculate total price from localStorage cart items
      const calculatedTotalPrice = calculateTotalPrice(localData.cartItems)

      // Use context values as fallbacks
      const finalCartItems = localData.cartItems.length > 0 ? localData.cartItems : items
      const finalBoxSize = localData.boxSize || boxSize
      const finalOrderType = localData.orderType || orderType
      const finalDeliveryTiming = localData.deliveryTiming || deliveryTiming
      const finalAddressDetails = localData.addressDetails.address ? localData.addressDetails : addressDetails
      const finalUserLocation = localData.userLocation.lat ? localData.userLocation : userLocation
      const finalOrderSchedule = localData.orderSchedule || orderSchedule
      const finalTotalPrice = calculatedTotalPrice > 0 ? calculatedTotalPrice : totalPrice

      // Update the businessInfo object without reloading scripts
      if (window.businessInfo) {
        window.businessInfo = {
          ...window.businessInfo,
          orderType: finalOrderType,
          userLocation: {
            lat: finalUserLocation.lat || selectedBranch?.lat || "0",
            lng: finalUserLocation.lng || selectedBranch?.lng || "0",
          },
          branchId: selectedBranch?.id.toString() || "18784",
          subscriptionType:
            finalDeliveryTiming === "weekly"
              ? "weekly"
              : finalDeliveryTiming === "biweekly"
                ? "bi-weekly"
                : finalDeliveryTiming === "later"
                  ? "schedule"
                  : "",
          addressDetails: {
            address: finalAddressDetails.address || "",
            city: finalAddressDetails.city || "",
            appartment: finalAddressDetails.appartment || "",
            area: finalAddressDetails.area || "",
            country: finalAddressDetails.country || "",
            postalCode: finalAddressDetails.postalCode || "",
          },
          orderSchedule: finalOrderSchedule || { date: "", time: "" },
          items: finalCartItems.map((item) => ({
            id: item.id.toString(),
            name: item.name,
            price: item.price.toString(),
            qty: item.quantity,
            image: item.image,
            category_name: item.isVeg ? "Veg" : "Non-Veg",
          })),
          total_price: finalTotalPrice,
          box_size: finalBoxSize || null,
        }
      }
    }
  }, [
    items,
    boxSize,
    orderType,
    totalPrice,
    selectedBranch,
    deliveryTiming,
    userLocation,
    addressDetails,
    orderSchedule,
    cartChecked,
  ])

  return (
    <div className="min-h-screen bg-gray-50" key={pageKey}>
      {/* Include external CSS */}
      <link rel="stylesheet" href="https://checkout.ordrz.com/static/css/main.a666252e.css" />

      {/* Include Google Fonts */}
      <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans&display=swap" rel="stylesheet" />

      {/* Simple loading indicator instead of skeleton */}
      {isLoading && (
        <div className="flex justify-center items-center h-64">
          <Loader2 className="w-12 h-12 animate-spin text-gray-400" />
        </div>
      )}

      {/* Show error message if scripts failed to load */}
      {scriptError && (
        <div className="text-center py-10">
          <div className="bg-red-50 border border-red-200 rounded-lg p-6 max-w-md mx-auto">
            <h2 className="text-xl font-semibold text-red-700 mb-2">Error Loading Checkout</h2>
            <p className="text-red-600 mb-4">{scriptError}</p>
            <button
              onClick={() => {
                setIsLoading(true)
                setScriptError(null)
                scriptsLoadedRef.current = false
                initializeCheckout()
              }}
              className="bg-black text-white px-6 py-2 rounded-md"
            >
              Try Again
            </button>
          </div>
        </div>
      )}

      {/* This is the container where the external React app will mount */}
      <div id="root"></div>
    </div>
  )
}
