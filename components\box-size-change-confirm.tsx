"use client"

import { useCart, type BoxSize } from "@/context/cart-context"
import { X } from "lucide-react"
import { useRef, useEffect } from "react"

type BoxSizeChangeConfirmProps = {
  isOpen: boolean
  onClose: () => void
  newBoxSize: BoxSize
}

export default function BoxSizeChangeConfirm({ isOpen, onClose, newBoxSize }: BoxSizeChangeConfirmProps) {
  const { setBoxSize, clearCart } = useCart()
  const modalRef = useRef<HTMLDivElement>(null)

  // Prevent clicks inside the modal from closing the cart
  useEffect(() => {
    const handleClick = (e: MouseEvent) => {
      if (modalRef.current && modalRef.current.contains(e.target as Node)) {
        e.stopPropagation()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClick, true)
    }

    return () => {
      document.removeEventListener("mousedown", handleClick, true)
    }
  }, [isOpen])

  if (!isOpen) return null

  const handleConfirm = () => {
    clearCart()
    setBoxSize(newBoxSize)
    onClose()
  }

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-[99999] flex items-center justify-center p-4"
      onClick={(e) => e.stopPropagation()} // Prevent clicks from reaching the document
    >
      <div
        ref={modalRef}
        className="bg-white rounded-lg max-w-md w-full p-6"
        onClick={(e) => e.stopPropagation()} // Extra safety to prevent propagation
      >
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-xl font-normal">Change Box Size?</h2>
          <button
            onClick={(e) => {
              e.stopPropagation() // Prevent propagation
              onClose()
            }}
            className="text-gray-500 hover:text-gray-700"
          >
            <X size={24} />
          </button>
        </div>

        <p className="mb-6">
          Changing your box size will empty your current cart. Would you like to continue with a new {newBoxSize}-meal
          box?
        </p>

        <div className="flex flex-col-reverse sm:flex-row justify-end space-y-reverse space-y-4 sm:space-y-0 sm:space-x-4">
          <button
            onClick={(e) => {
              e.stopPropagation() // Prevent propagation
              onClose()
            }}
            className="px-4 py-2 border border-gray-300 rounded-md hover:bg-gray-50 w-full sm:w-auto"
          >
            Cancel
          </button>

          <button
            onClick={(e) => {
              e.stopPropagation() // Prevent propagation
              handleConfirm()
            }}
            className="px-4 py-2 bg-black text-white rounded-md hover:bg-gray-800 w-full sm:w-auto mb-4 sm:mb-0"
          >
            Switch & Clear Cart
          </button>
        </div>
      </div>
    </div>
  )
}
