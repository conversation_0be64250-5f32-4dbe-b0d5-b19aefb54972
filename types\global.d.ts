interface Window {
  businessInfo: {
    cartId: string;
    businessId: number;
    orderType: string;
    branchId: string;
    source: string;
    theme: {
      header_font_color: string;
      header_bg: string;
      body_font_color: string;
      body_bg: string;
      button_font_color: string;
      button_bg: string;
      button_radius: string;
      product_card: {
        font_color: string;
        desc_color: string;
        background_color: string;
        border_color: string;
        border_radius: string;
      };
    };
    userLocation: {
      lat: string;
      lng: string;
    };
    orderSchedule: {
      date: string;
      time: string;
    };
    websiteLink: string;
    items: Array<{
      id: string;
      name: string;
      price: string;
      qty: number;
      image: string;
      category_name: string;
    }>;
    total_price: number;
    box_size: number | null;
  };
  google: any;
  initGoogleMapsAutocomplete: () => void;
  gtag: (
    command: string,
    action: string,
    params?: {
      [key: string]: any;
    }
  ) => void;
  dataLayer: Array<{
    event?: string;
    ecommerce?: {
      currency?: string;
      value?: number;
      items?: Array<{
        item_id: string;
        item_name: string;
        price?: number;
        item_category?: string;
        quantity?: number;
      }>;
    };
    [key: string]: any;
  }>;
}
