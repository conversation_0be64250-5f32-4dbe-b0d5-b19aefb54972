export type ReviewSubmission = {
  productId: string | number
  name: string
  email?: string
  phone?: string
  rating: number
  comment: string
  title?: string
  userId?: string
}

export async function submitProductReview(review: ReviewSubmission): Promise<{
  success: boolean
  message: string
  data?: any
  error?: any
}> {
  try {
    // Ensure productId is a string
    const formattedReview = {
      ...review,
      productId: review.productId.toString(),
    }

    const response = await fetch("/api/reviews", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formattedReview),
    })

    const result = await response.json()

    if (!response.ok) {
      return {
        success: false,
        message: result.message || "Failed to submit review",
        error: result.error || result.details || null,
      }
    }

    return {
      success: true,
      message: "Review submitted successfully",
      data: result.data,
    }
  } catch (error) {
    console.error("Error submitting review:", error)
    return {
      success: false,
      message: "An error occurred while submitting the review",
      error: error instanceof Error ? error.message : String(error),
    }
  }
}
