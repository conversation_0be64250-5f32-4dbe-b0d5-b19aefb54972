import { BUSINESS_ID } from "@/constants/app-constants"

export interface ProductReviewUser {
  reviewid: string
  business_id: string
  product_id: string
  user_id: string
  user_name: string
  source: string
  rating: string
  comment: string
  date: string
  product_rating: string
  product_name: string
}

export interface ProductReviews {
  product_id: string
  product_name: string
  rating: string
  total_reviews: number
  users: ProductReviewUser[]
  ratings: {
    one: number
    two: number
    three: number
    four: number
    five: number
  }
}

export interface ProductDetailResponse {
  items: Array<{
    menu_item_id: string
    name: string
    price: string
    currency: string
    desc: string
    product_rating: string
    product_reviews: ProductReviews
    sku: string
    status: string
    image: string
    large_image: string
    images: Array<{
      image: string
      image_thumbnail: string
      image_id: string
      image_position: string
    }>
    attributes: Array<{
      attr_id: number
      attr_name: string
      option_id: number
      option_name: string
    }>
    category: string
    // Additional fields as needed
  }>
}

export async function fetchProductDetail(productId: string | number): Promise<ProductDetailResponse> {
  try {
    const response = await fetch(
      `https://tossdown.com/api/product_details?item_id=${productId}&business_id=${BUSINESS_ID}`,
      {
        headers: {
          "Content-Type": "application/json",
        },
        cache: "no-store", // Disable caching to always get fresh data
      },
    )

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`)
    }

    const data = await response.json()
    return data
  } catch (error) {
    console.error("Error fetching product details:", error)
    throw error
  }
}

function extractProductIdFromSlug(slug: string): string {
  // Basic implementation: Assumes the slug ends with -[product_id]
  const parts = slug.split("-")
  const productId = parts[parts.length - 1]
  return productId
}

// Add this function to fetch product by slug
export async function fetchProductBySlug(slug: string) {
  try {
    const productId = extractProductIdFromSlug(slug)
    return await fetchProductDetail(productId)
  } catch (error) {
    console.error("Error fetching product by slug:", error)
    throw error
  }
}

// Update the isProductVegetarian function to use the attributes array
export function isProductVegetarian(product: any): boolean {
  if (!product?.attributes || !Array.isArray(product.attributes)) return false

  // Check if there's an attribute with key "Veg" and value "Veg"
  if (product.attributes.length > 0) {
    const firstAttribute = product.attributes[0]
    return Object.keys(firstAttribute).includes("Veg") && firstAttribute["Veg"] === "Veg"
  }

  return false
}

// Map the API response to a more usable format
export function mapProductDetailToViewModel(productDetail: ProductDetailResponse) {
  if (!productDetail?.items?.length) return null

  const product = productDetail.items[0]

  // Map reviews to a more usable format
  const reviews =
    product.product_reviews?.users?.map((user) => ({
      id: user.reviewid,
      user_name: user.user_name,
      date: user.date?.split(" ")[0] || "", // Extract just the date part
      rating: Number.parseInt(user.rating) || 0,
      comment: user.comment,
    })) || []

  return {
    id: Number.parseInt(product.menu_item_id),
    name: product.name,
    price: Number.parseFloat(product.price),
    currency: product.currency,
    description: product.desc,
    image: product.image || product.large_image,
    rating: Number.parseFloat(product.product_rating) || 0,
    isVeg: isProductVegetarian(product),
    sku: product.sku,
    status: product.status,
    inStock: product.status !== "1",
    reviews,
    totalReviews: product.product_reviews?.total_reviews || 0,
    // Add more fields as needed
  }
}
