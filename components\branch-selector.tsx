"use client"

import { useState } from "react"
import { useBusiness } from "@/context/business-context"
import { MapPin } from "lucide-react"
import { getFormattedBranchAddress } from "@/services/business-api"
import { BranchSelectorSkeleton } from "./branch-selector-skeleton"

export default function BranchSelector() {
  const { businessData, selectedBranch, setSelectedBranch, isLoading } = useBusiness()
  const [isOpen, setIsOpen] = useState(false)

  // Show skeleton while loading
  if (isLoading) {
    return <BranchSelectorSkeleton />
  }

  // If no business data or only one branch, don't show selector
  if (
    !businessData ||
    !businessData.result ||
    !businessData.result.branches ||
    businessData.result.branches.length <= 1
  ) {
    return null
  }

  const branches = businessData.result.branches.filter((branch) => branch.pickup === 1)

  // If no pickup branches, don't show selector
  if (branches.length === 0) {
    return null
  }

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="flex items-center gap-2 p-2 border border-gray-300 rounded-md w-full text-left"
      >
        <MapPin size={18} />
        <span className="flex-1 truncate">
          {selectedBranch ? getFormattedBranchAddress(selectedBranch) : "Select pickup location"}
        </span>
        <svg
          className={`h-5 w-5 transition-transform ${isOpen ? "rotate-180" : ""}`}
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white border border-gray-300 rounded-md shadow-lg max-h-60 overflow-auto">
          {branches.map((branch) => (
            <button
              key={branch.id}
              className={`w-full text-left px-4 py-2 hover:bg-gray-100 ${
                selectedBranch?.id === branch.id ? "bg-gray-100" : ""
              }`}
              onClick={() => {
                setSelectedBranch(branch)
                setIsOpen(false)
              }}
            >
              <div className="font-medium">{branch.address}</div>
              <div className="text-sm text-gray-500">
                {branch.city}, {branch.location}
              </div>
            </button>
          ))}
        </div>
      )}
    </div>
  )
}
