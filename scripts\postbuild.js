const fs = require("fs");
const path = require("path");
const https = require("https");
const http = require("http");

// Configuration
const SITE_URL = "https://ezeats.ca";
const OUTPUT_PATH = path.join(process.cwd(), "public", "sitemap.xml");

/**
 * Fetches data from a URL
 * @param {string} url - The URL to fetch data from
 * @returns {Promise<any>} - The parsed JSON data
 */
function fetchData(url) {
  return new Promise((resolve, reject) => {
    const client = url.startsWith("https") ? https : http;

    client
      .get(url, (res) => {
        let data = "";

        res.on("data", (chunk) => {
          data += chunk;
        });

        res.on("end", () => {
          try {
            resolve(JSON.parse(data));
          } catch (e) {
            reject(new Error(`Failed to parse JSON: ${e.message}`));
          }
        });
      })
      .on("error", (err) => {
        reject(err);
      });
  });
}

/**
 * Generates a URL-friendly slug from a product name and ID
 * @param {string} name - The product name
 * @param {string|number} id - The product ID
 * @returns {string} - The generated slug
 */
function generateProductSlug(name, id) {
  // Convert the name to lowercase, replace spaces with hyphens, and remove special characters
  const nameSlug = name
    .toLowerCase()
    .replace(/[^\w\s-]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with a single hyphen
    .trim();

  // Combine the name slug with the ID
  return `${nameSlug}-${id}`;
}

/**
 * Generates a static sitemap.xml file
 */
async function generateSitemap() {
  try {
    console.log("Generating static sitemap.xml...");

    // Get today's date in YYYY-MM-DD format
    const today = new Date().toISOString().split("T")[0];

    // Define main pages
    const mainPages = [
      { url: "/", changefreq: "weekly", priority: 1.0 },
      { url: "/menu", changefreq: "daily", priority: 0.9 },
      { url: "/our-menu", changefreq: "daily", priority: 0.9 },
      { url: "/our-kitchen", changefreq: "monthly", priority: 0.8 },
      { url: "/contact-us", changefreq: "monthly", priority: 0.7 },
      {
        url: "/vegetarian-meal-delivery",
        changefreq: "monthly",
        priority: 0.8,
      },
      { url: "/halal-meal-delivery", changefreq: "monthly", priority: 0.8 },
      { url: "/terms-and-conditions", changefreq: "monthly", priority: 0.8 },
      { url: "/privacy-policy", changefreq: "monthly", priority: 0.8 },
      { url: "/profile", changefreq: "monthly", priority: 0.5 },
      { url: "/profile/orders", changefreq: "monthly", priority: 0.4 },
      { url: "/profile/subscriptions", changefreq: "monthly", priority: 0.4 },
      { url: "/profile/activity-log", changefreq: "monthly", priority: 0.3 },
    ];

    // Start generating XML
    let xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
  <!-- Homepage -->
  <url>
    <loc>${SITE_URL}/</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>1.0</priority>
  </url>

  <!-- Main Pages -->`;

    // Add main pages (excluding homepage which is already added)
    mainPages.slice(1).forEach((page) => {
      xml += `
  <url>
    <loc>${SITE_URL}${page.url}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>${page.changefreq}</changefreq>
    <priority>${page.priority}</priority>
  </url>`;
    });

    // Try to fetch products and add them to the sitemap
    try {
      console.log("Fetching products for sitemap...");

      // Fetch products from the API
      const productsApiUrl =
        "https://tossdown.com/api/products?display_source=2&business_id=12536&branch_id=18784&attributes=1";
      const productsData = await fetchData(productsApiUrl);

      if (productsData && productsData.items && productsData.items.length > 0) {
        // Filter valid products (display_source === "0" || display_source === "2")
        const validProducts = productsData.items.filter(
          (product) =>
            product.display_source === "0" || product.display_source === "2"
        );

        // Add products to sitemap
        xml += `

  <!-- Products -->`;

        validProducts.forEach((product) => {
          const slug = generateProductSlug(product.name, product.menu_item_id);
          xml += `
  <url>
    <loc>${SITE_URL}/product/${slug}</loc>
    <lastmod>${today}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>0.8</priority>
  </url>`;
        });

        console.log(`Added ${validProducts.length} products to sitemap`);
      }
    } catch (error) {
      console.error("Error fetching products for sitemap:", error);
      // Continue with the sitemap generation even if product fetching fails
      xml += `

  <!-- Products and categories could not be fetched -->
  <!-- Please check the dynamic sitemap at /api/sitemap for complete listings -->`;
    }

    // Close the XML
    xml += `
</urlset>`;

    // Write the XML to the output file
    fs.writeFileSync(OUTPUT_PATH, xml);
    console.log(`Static sitemap.xml generated at: ${OUTPUT_PATH}`);

    console.log("Sitemap generation completed successfully!");
  } catch (error) {
    console.error("Error generating sitemap:", error);
    process.exit(1);
  }
}

// Run the script
generateSitemap();
