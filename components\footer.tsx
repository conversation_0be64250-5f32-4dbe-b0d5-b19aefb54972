"use client";

import Link from "next/link";
import { useBusiness } from "@/context/business-context";
import { useEffect, useState } from "react";

export default function Footer() {
  const businessContext = useBusiness();
  const [businessData, setBusinessData] = useState({
    businessLogo: null,
    businessName: "EZeats",
  });
  const [error, setError] = useState(null);

  // Safely get business data
  useEffect(() => {
    try {
      if (businessContext) {
        setBusinessData({
          businessLogo: businessContext.businessLogo,
          businessName: businessContext.businessName,
        });
      }
    } catch (err) {
      console.error("Error accessing business context in footer:", err);
      setError(err);
    }
  }, [businessContext]);

  // Add this useEffect hook to implement the accordion functionality for mobile
  useEffect(() => {
    try {
      if (typeof document !== "undefined") {
        var acc = document.getElementsByClassName("accordion");
        var i;

        for (i = 0; i < acc.length; i++) {
          acc[i].addEventListener("click", function () {
            this.classList.toggle("active");
            var panel = this.nextElementSibling;
            if (panel && panel.style) {
              if (panel.style.display === "block") {
                panel.style.display = "none";
              } else {
                panel.style.display = "block";
              }
            }
          });
        }
      }
    } catch (err) {
      console.error("Error setting up accordion in footer:", err);
    }
  }, []); // Empty dependency array ensures this runs only once after mount

  // If there was an error accessing the context, render a simplified footer
  if (error) {
    return (
      <footer className="bg-black text-white py-8">
        <div className="container mx-auto px-4">
          <p className="text-center">
            © {new Date().getFullYear()} EZeats. All rights reserved.
          </p>
        </div>
      </footer>
    );
  }

  return (
    <>
      <section className="footer-section">
        <div className="custom-container">
          <div className="footer-data-box">
            <div className="footer-data-box-detail ">
              <div className="footer-detail-box-one">
                <div className="flex items-center mb-5">
                  <figure className="pb-0 mb-0">
                    <img
                      src="https://images-beta.tossdown.com/site/9e313078-7418-407e-aa05-fd244df31d5a.webp"
                      alt="Footer logo"
                    />
                  </figure>
                  <img
                    src="https://static.tossdown.com/site/9cae0094-a2b4-41b0-a2e9-3d038f40eed0.webp"
                    alt="HMA"
                    width="65px"
                    className="ml-5"
                  />
                </div>
                <p>Your affordable meal solution!</p>
                <div className="footer-socail-links">
                  <Link
                    href="https://www.facebook.com/share/1ESo81z7ga/?mibextid=wwXIfr"
                    target="_blank"
                  >
                    <i className="fab fa-facebook-f" aria-hidden="true"></i>
                  </Link>{" "}
                  <Link
                    href="https://www.instagram.com/ezeats.ca?igsh=dG42ZTYzYm9ldHpm"
                    target="_blank"
                  >
                    <i className="fab fa-instagram" aria-hidden="true"></i>
                  </Link>{" "}
                  <Link
                    href="https://www.tiktok.com/@ezeats.ca?_t=ZS-8vx9kdn0LAY&_r=1"
                    target="_blank"
                  >
                    <i className="fab fa-tiktok" aria-hidden="true"></i>
                  </Link>
                </div>
              </div>
            </div>

            <div className="footer-data-box-detail">
              <div className="footer-detail-box-two">
                <div className="footer-detail-box-heading accordion modtitle">
                  <h3 className="footer-collapse-icons">Quick Links</h3>
                </div>
                <div className="modcontent panel">
                  <ul className="footer-detail-box-content-list">
                    <li>
                      <Link href="/">HOME</Link>
                    </li>
                    <li>
                      <Link href="/our-menu">OFFERED IN MENU</Link>
                    </li>
                    <li>
                      <Link href="/our-kitchen">OUR KITCHEN</Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="footer-data-box-detail">
              <div className="footer-detail-box-two">
                <div className="footer-detail-box-heading accordion modtitle">
                  <h3 className="footer-collapse-icons">Useful Links</h3>
                </div>
                <div className="modcontent panel">
                  <ul className="footer-detail-box-content-list">
                    <li>
                      <Link href="/terms-and-conditions">
                        Terms &amp; Conditions
                      </Link>
                    </li>
                    <li>
                      <Link href="/privacy-policy">Privacy Policy</Link>
                    </li>
                    <li>
                      <Link href="#">Our Team</Link>
                    </li>
                    <li>
                      <Link href="#">Blogs</Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="footer-data-box-detail">
              <div className="footer-detail-box-two">
                <div className="footer-detail-box-heading accordion modtitle">
                  <h3 className="footer-collapse-icons">site links</h3>
                </div>
                <div className="modcontent panel">
                  <ul className="footer-detail-box-content-list">
                    <li>
                      <Link href="/vegetarian-meal-delivery">
                        Vegetarian Meal Delivery
                      </Link>
                    </li>
                    <li>
                      <Link href="/halal-meal-delivery">
                        Halal Meal Delivery
                      </Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>

            <div className="footer-data-box-detail">
              <div className="footer-detail-box-two ">
                <div className="footer-detail-box-heading accordion modtitle">
                  <h3 className="footer-collapse-icons">Contact Us</h3>
                </div>
                <div className="modcontent panel accordion modtitle">
                  <ul className="footer-contact-list ">
                    <li>
                      <Link href="tel: ******-688-6874">******-688-6874</Link>
                    </li>
                    <li>
                      <Link href="mailto:<EMAIL>"><EMAIL></Link>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div className="copyright_footer">
        <div
          className="w-full text-center py-2.5 bg-black"
          style={{ borderTop: "1px solid #FFFFFF1A" }}
        >
          <p className="text-sm text-white mb-0">
            © {new Date().getFullYear()} EZeats.
          </p>
        </div>
      </div>
    </>
  );
}
