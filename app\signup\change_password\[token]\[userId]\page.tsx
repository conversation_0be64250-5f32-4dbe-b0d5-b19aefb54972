"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { Eye, EyeOff, ArrowLeft } from "lucide-react"
import { useToast } from "@/context/toast-context"
import { useRouter, useParams } from "next/navigation"
import Link from "next/link"
import { BUSINESS_ID } from "@/constants/app-constants"

export default function ResetPasswordPage() {
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const { showToast } = useToast()
  const router = useRouter()
  const params = useParams()

  // Extract token and userId from URL params
  const token = params?.token as string
  const userId = params?.userId as string

  // Validate that we have the required parameters
  useEffect(() => {
    if (!token || !userId) {
      showToast({
        message: "Invalid password reset link. Please request a new one.",
        type: "error",
        duration: 4000,
      })
    }
  }, [token, userId, showToast])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    // Basic validation
    if (password !== confirmPassword) {
      showToast({
        message: "Passwords do not match",
        type: "error",
        duration: 4000,
      })
      return
    }

    if (password.length < 6) {
      showToast({
        message: "Password must be at least 6 characters long",
        type: "error",
        duration: 4000,
      })
      return
    }

    setIsLoading(true)

    try {
      const response = await fetch(
        `https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/user/reset-password`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            token,
            newPassword: password,
            confirmPassword,
            userId,
          }),
        },
      )

      const data = await response.json()

      if (response.ok && data.status === 200) {
        showToast({
          message: "Password reset successful! You can now login with your new password.",
          type: "success",
          duration: 5000,
        })

        // Redirect to home page after successful password reset
        setTimeout(() => {
          router.push("/")
        }, 2000)
      } else {
        showToast({
          message: data.message || "Failed to reset password. Please try again.",
          type: "error",
          duration: 4000,
        })
      }
    } catch (error) {
      console.error("Password reset error:", error)
      showToast({
        message: "An error occurred. Please try again later.",
        type: "error",
        duration: 4000,
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 p-4 font-poppins">
      <div className="bg-white rounded-lg w-full max-w-md overflow-hidden shadow-lg">
        <div className="p-6">
          <div className="flex items-center mb-6">
            <Link href="/" className="text-gray-500 hover:text-gray-700 mr-4">
              <ArrowLeft size={20} />
            </Link>
            <h2 className="text-2xl font-anton uppercase tracking-wide">Reset Password</h2>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="password" className="block text-sm font-medium text-gray-600 mb-1">
                New Password
              </label>
              <div className="relative">
                <input
                  type={showPassword ? "text" : "password"}
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md pr-10 font-poppins"
                  placeholder="Enter new password"
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>

            <div className="mb-6">
              <label htmlFor="confirmPassword" className="block text-sm font-medium text-gray-600 mb-1">
                Confirm New Password
              </label>
              <div className="relative">
                <input
                  type={showConfirmPassword ? "text" : "password"}
                  id="confirmPassword"
                  value={confirmPassword}
                  onChange={(e) => setConfirmPassword(e.target.value)}
                  className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md pr-10 font-poppins"
                  placeholder="Confirm new password"
                  required
                />
                <button
                  type="button"
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 bg-black text-white rounded-md font-medium hover:bg-gray-900 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
            >
              {isLoading ? "Resetting Password..." : "Reset Password"}
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}
