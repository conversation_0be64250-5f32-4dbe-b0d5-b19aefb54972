import type { Metadata } from "next"
import { getPageSeo, getProductSeo } from "@/services/seo-service"

// Default metadata as fallback (only for title)
const DEFAULT_TITLE = "EZeats - Fresh Meal Delivery"

/**
 * Generates metadata for a specific page using its XML page name
 */
export async function generatePageMetadata(pageName: string): Promise<Metadata> {
  console.log(`Generating metadata for page: ${pageName}`)

  try {
    const pageSeo = await getPageSeo(pageName)

    if (!pageSeo) {
      console.warn(`Using default title for page: ${pageName}`)
      return {
        title: DEFAULT_TITLE,
      }
    }

    // Create metadata object with only the values from XML
    const metadata: Metadata = {
      title: pageSeo.seo.pagetitle || DEFAULT_TITLE,
    }

    // Only add description if it exists in the XML
    if (pageSeo.seo.desc) {
      metadata.description = pageSeo.seo.desc
    }

    // Only add keywords if they exist in the XML
    if (pageSeo.seo.keywords) {
      metadata.keywords = pageSeo.seo.keywords
    }

    return metadata
  } catch (error) {
    console.error(`<PERSON>rror generating metadata for page ${pageName}:`, error)
    return {
      title: DEFAULT_TITLE,
    }
  }
}

/**
 * Generates metadata for a product detail page
 */
export async function generateProductMetadata(productName: string): Promise<Metadata> {
  console.log(`Generating metadata for product: ${productName}`)

  try {
    const productSeo = await getProductSeo(productName)

    if (!productSeo) {
      return {
        title: `${productName} - EZeats`,
      }
    }

    // Create metadata object with only the values from XML
    const metadata: Metadata = {
      title: productSeo.pagetitle || `${productName} - EZeats`,
    }

    // Only add description if it exists
    if (productSeo.desc) {
      metadata.description = productSeo.desc
    }

    // Only add keywords if they exist
    if (productSeo.keywords) {
      metadata.keywords = productSeo.keywords
    }

    return metadata
  } catch (error) {
    console.error(`Error generating metadata for product ${productName}:`, error)
    return {
      title: `${productName} - EZeats`,
    }
  }
}
