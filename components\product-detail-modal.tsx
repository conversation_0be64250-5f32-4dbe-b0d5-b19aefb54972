"use client";
import { X } from "lucide-react";
import { useState, useEffect, useRef } from "react";
import type { ProductReview } from "@/types/product";
import ProductDetailContent from "./product-detail-content";

// Near the top of the file, import our new API functions
import {
  fetchProductDetail,
  mapProductDetailToViewModel,
} from "@/services/product-detail-api";

// Simple function to extract and clean description content
function extractDescription(description = "") {
  // Remove the custom tags but keep their content
  const cleanedDesc = description
    .replace(/<short_desc>(.*?)<\/short_desc>/gs, "")
    .replace(/<long_desc>(.*?)<\/long_desc>/gs, "$1")
    .trim();

  return cleanedDesc;
}

// After the extractDescription function, add a new function to calculate average rating

function calculateAverageRating(reviews?: ProductReview[]): number {
  if (!reviews || reviews.length === 0) return 0;
  const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
  return sum / reviews.length;
}

type ProductDetailModalProps = {
  isOpen: boolean;
  onClose: () => void;
  productId: number | null;
};

export default function ProductDetailModal({
  isOpen,
  onClose,
  productId,
}: ProductDetailModalProps) {
  const [product, setProduct] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const modalRef = useRef<HTMLDivElement>(null);
  const [bootstrapLoaded, setBootstrapLoaded] = useState(false);
  const [jqueryLoaded, setJqueryLoaded] = useState(false);
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);

  // Update the handleSubmitReview function to close the product detail modal
  const handleSubmitReview = async (reviewData: {
    rating: number;
    name: string;
    comment: string;
    productId: string;
  }) => {
    try {
      // For now, we'll just add it to the product's reviews in state
      const newReview = {
        id: `review-${Date.now()}`,
        user_name: reviewData.name,
        date: new Date().toISOString().split("T")[0],
        rating: reviewData.rating,
        comment: reviewData.comment,
      };

      setProduct((prev) => ({
        ...prev,
        reviews: [...(prev.reviews || []), newReview],
      }));

      // Close the review modal but don't close the product detail modal
      setIsReviewModalOpen(false);

      return Promise.resolve();
    } catch (error) {
      console.error("Error handling review submission:", error);
      return Promise.reject(error);
    }
  };

  // Load necessary resources when modal is opened
  useEffect(() => {
    if (isOpen) {
      // Create a link element for Bootstrap CSS
      const bootstrapLink = document.createElement("link");
      bootstrapLink.rel = "stylesheet";
      bootstrapLink.href =
        "https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/css/bootstrap.min.css";
      bootstrapLink.integrity =
        "sha384-Vkoo8x4CGsO3+Hhxv8T/Q5PaXtkKtu6ug5TOeNV6gBiFeWPGFN9MuhOf23Q9Ifjh";
      bootstrapLink.crossOrigin = "anonymous";
      bootstrapLink.id = "bootstrap-css-modal";

      // Create a link element for Font Awesome
      const fontAwesomeLink = document.createElement("link");
      fontAwesomeLink.rel = "stylesheet";
      fontAwesomeLink.href =
        "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css";
      fontAwesomeLink.integrity =
        "sha512-1ycn6IcaQQ40/MKBW2W4Rhis/DbILU74C1vSrLJxCq57o941Ym01SwNsOMqvEBFlcgUa6xLiPY/NS5R+E6ztJQ==";
      fontAwesomeLink.crossOrigin = "anonymous";
      fontAwesomeLink.id = "font-awesome-css-modal";

      // Create a link element for custom product description CSS
      const customCssLink = document.createElement("link");
      customCssLink.rel = "stylesheet";
      customCssLink.href = "/styles/product-description.css";
      customCssLink.id = "product-description-css-modal";

      // Add onload handler for Bootstrap CSS
      bootstrapLink.onload = () => {
        setBootstrapLoaded(true);
      };

      // Append the CSS links to the head
      document.head.appendChild(bootstrapLink);
      document.head.appendChild(fontAwesomeLink);
      document.head.appendChild(customCssLink);

      // Create script elements for jQuery and Bootstrap JS
      const jqueryScript = document.createElement("script");
      jqueryScript.src = "https://code.jquery.com/jquery-3.4.1.slim.min.js";
      jqueryScript.integrity =
        "sha384-J6qa4849blE2+poT4WnyKhv5vZF5SrPo0iEjwBvKU7imGFAV0wwj1yYfoRSJoZ+n";
      jqueryScript.crossOrigin = "anonymous";
      jqueryScript.id = "jquery-script-modal";

      // Add onload handler for jQuery
      jqueryScript.onload = () => {
        setJqueryLoaded(true);

        // Add Popper.js after jQuery is loaded
        const popperScript = document.createElement("script");
        popperScript.src =
          "https://cdn.jsdelivr.net/npm/popper.js@1.16.0/dist/umd/popper.min.js";
        popperScript.integrity =
          "sha384-Q6E9RHvbIyZFJoft+2mJbHaEWldlvI9IOYy5n3zV9zzTtmI3UksdQRVvoxMfooAo";
        popperScript.crossOrigin = "anonymous";
        popperScript.id = "popper-script-modal";
        document.body.appendChild(popperScript);

        // Add Bootstrap JS after Popper.js
        const bootstrapScript = document.createElement("script");
        bootstrapScript.src =
          "https://cdn.jsdelivr.net/npm/bootstrap@4.4.1/dist/js/bootstrap.min.js";
        bootstrapScript.integrity =
          "sha384-wfSDF2E50Y2D1uUdj0O3uMBJnjuUD4Ih7YwaYd1iqfktj0Uod8GCExl3Og8ifwB6";
        bootstrapScript.crossOrigin = "anonymous";
        bootstrapScript.id = "bootstrap-script-modal";
        document.body.appendChild(bootstrapScript);
      };

      // Append jQuery script to the body
      document.body.appendChild(jqueryScript);

      // Return a cleanup function to remove the elements when the modal is closed
      return () => {
        const bootstrapLink = document.getElementById("bootstrap-css-modal");
        const fontAwesomeLink = document.getElementById(
          "font-awesome-css-modal"
        );
        const customCssLink = document.getElementById(
          "product-description-css-modal"
        );
        const jqueryScript = document.getElementById("jquery-script-modal");
        const popperScript = document.getElementById("popper-script-modal");
        const bootstrapScript = document.getElementById(
          "bootstrap-script-modal"
        );

        if (bootstrapLink) document.head.removeChild(bootstrapLink);
        if (fontAwesomeLink) document.head.removeChild(fontAwesomeLink);
        if (customCssLink) document.head.removeChild(customCssLink);
        if (jqueryScript) document.body.removeChild(jqueryScript);
        if (popperScript) document.body.removeChild(popperScript);
        if (bootstrapScript) document.body.removeChild(bootstrapScript);

        setBootstrapLoaded(false);
        setJqueryLoaded(false);
      };
    }
  }, [isOpen]);

  // Replace the useEffect that loads product data with this updated version:

  useEffect(() => {
    async function loadProduct() {
      if (!productId) return;

      setIsLoading(true);
      setError(null);

      try {
        // Use the new API to fetch product details
        const productDetails = await fetchProductDetail(productId);
        if (
          productDetails &&
          productDetails.items &&
          productDetails.items.length > 0
        ) {
          // Map the response to our view model
          const mappedProduct = mapProductDetailToViewModel(productDetails);
          if (mappedProduct) {
            setProduct(mappedProduct);
          } else {
            setError("Failed to parse product details");
          }
        } else {
          setError("Product not found");
        }
      } catch (err) {
        console.error("Error loading product:", err);
        setError("Failed to load product details");
      } finally {
        setIsLoading(false);
      }
    }

    if (isOpen && productId) {
      loadProduct();
    }
  }, [isOpen, productId]);

  // Initialize Bootstrap collapse functionality after the component renders and jQuery is loaded
  useEffect(() => {
    if (isOpen && !isLoading && product && jqueryLoaded) {
      // Wait for jQuery and Bootstrap to be loaded
      const checkJQuery = setInterval(() => {
        if (window.jQuery && window.jQuery.fn.collapse) {
          clearInterval(checkJQuery);

          // Initialize all collapse elements
          window.jQuery(".collapse").collapse();

          // Add icon containers if they don't exist
          const headers = document.querySelectorAll(
            ".product-modal-content .log_des_acc_header"
          );
          headers.forEach((header) => {
            let iconContainer = header.querySelector(".acc_icon");
            if (!iconContainer) {
              iconContainer = document.createElement("div");
              iconContainer.className = "acc_icon";
              header.appendChild(iconContainer);
            }
          });
        }
      }, 100);

      return () => clearInterval(checkJQuery);
    }
  }, [isOpen, isLoading, product, jqueryLoaded]);

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-[12000] p-2"
      onClick={onClose} // Close when clicking the overlay
    >
      {/* Anti-Tailwind CSS Reset - Combined all styles into one tag */}
      <style jsx global>
        {`
          .collapse {
            visibility: visible;
          }

          /* Header link styles */
          header a {
            color: black !important;
            transition: color 0.2s ease;
          }

          header a:hover {
            color: #ffcc00 !important;
          }
        `}
      </style>
      <div
        ref={modalRef}
        className="bg-white rounded-lg !w-[94vw] relative container !p-[10px] md:!p-[24px] mx-auto my-8"
        onClick={(e) => e.stopPropagation()} // Prevent clicks inside the modal from closing it
      >
        {/* Close button positioned absolutely in the top right corner */}
        <button
          onClick={onClose}
          className="absolute -top-[11px] -right-[11px] bg-white p-2 rounded-full hover:bg-gray-100 shadow-md z-20"
        >
          <X size={24} />
        </button>

        {isLoading ? (
          <div className="flex justify-center items-center p-12">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
          </div>
        ) : error ? (
          <div className="p-8 text-center text-red-500">{error}</div>
        ) : product ? (
          <div className="pro_model_scroll max-h-[85vh] overflow-y-auto">
            <ProductDetailContent
              product={product}
              onModalClose={onClose}
              onReviewSubmit={async (reviewData) => {
                try {
                  // For now, we'll just add it to the product's reviews in state
                  const newReview = {
                    id: `review-${Date.now()}`,
                    user_name: reviewData.name,
                    date: new Date().toISOString().split("T")[0],
                    rating: reviewData.rating,
                    comment: reviewData.comment,
                  };

                  setProduct((prev) => ({
                    ...prev,
                    reviews: [...(prev.reviews || []), newReview],
                  }));

                  // Close the product detail modal
                  onClose();

                  return Promise.resolve();
                } catch (error) {
                  console.error("Error handling review submission:", error);
                  return Promise.reject(error);
                }
              }}
            />
          </div>
        ) : null}
      </div>
    </div>
  );
}
