import type { Metada<PERSON> } from "next"
import { generatePageMetadata } from "@/lib/metadata-utils"
import { getPageSeo } from "@/services/seo-service"
import ClientHomePage from "./client-page"

export async function generateMetadata(): Promise<Metadata> {
  // Use "home" XML page name for the home page
  return generatePageMetadata("home")
}

export default async function Home() {
  // Fetch SEO data server-side to pass to the client component
  const pageSeo = await getPageSeo("home")
  const h1 = pageSeo?.seo?.h1 || null

  return <ClientHomePage initialH1={h1} />
}
