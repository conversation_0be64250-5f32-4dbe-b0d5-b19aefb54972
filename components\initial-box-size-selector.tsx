"use client"

import { useEffect, useState } from "react"
import { useCart } from "@/context/cart-context"

export default function InitialBoxSizeSelector() {
  const { boxSize, setShowBoxSelector } = useCart()
  const [hasCheckedStorage, setHasCheckedStorage] = useState(false)

  useEffect(() => {
    // Check if we've already verified localStorage
    if (hasCheckedStorage) return

    // First, check localStorage directly to avoid race conditions
    const savedBoxSize = localStorage.getItem("boxSize")

    if (savedBoxSize) {
      try {
        // If we have a saved box size in localStorage, don't show the selector
        const parsedSize = JSON.parse(savedBoxSize)
        if (parsedSize === 6 || parsedSize === 12) {
          // Box size exists in localStorage, don't show selector
          setHasCheckedStorage(true)
          return
        }
      } catch (e) {
        console.error("Failed to parse box size from localStorage", e)
      }
    }

    // If we get here, either there's no box size in localStorage or it's invalid
    // Now check the boxSize state from context
    if (boxSize === null) {
      setShowBoxSelector(true)
    }

    setHasCheckedStorage(true)
  }, [boxSize, setShowBoxSelector, hasCheckedStorage])

  // This is a utility component that doesn't render anything visible
  return null
}
