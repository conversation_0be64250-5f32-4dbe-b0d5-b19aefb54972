/**
 * Google Tag Manager dataLayer and Meta Pixel utility functions for event tracking
 */

/**
 * Helper function to safely push to dataLayer
 * @param data The data to push to the dataLayer
 */
function pushToDataLayer(data: any): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    // Initialize dataLayer if it doesn't exist
    window.dataLayer = window.dataLayer || [];
    window.dataLayer.push(data);
  } catch (error) {
    console.error("Error pushing to dataLayer:", error);
  }
}

/**
 * Helper function to safely call Meta Pixel fbq function
 * @param event The event name
 * @param params Optional event parameters
 */
export function trackMetaPixelEvent(event: string, params?: any): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    // Check if fbq function exists
    if (typeof (window as any).fbq === "function") {
      if (params) {
        (window as any).fbq("track", event, params);
      } else {
        (window as any).fbq("track", event);
      }
    }
  } catch (error) {
    console.error(`Error tracking Meta Pixel event ${event}:`, error);
  }
}

/**
 * Track a page view event
 * @param path The page path (e.g., '/home', '/product/123')
 * @param title The page title
 */
export function trackPageView(path: string, title: string): void {
  pushToDataLayer({
    event: "page_view",
    page_path: path,
    page_title: title,
    page_location: typeof window !== "undefined" ? window.location.href : "",
  });
}

/**
 * Track a view item event when a user views a product
 * @param product The product being viewed
 */
export function trackViewItem(product: {
  id: number | string;
  name: string;
  price?: number;
  category?: string;
}): void {
  // Clear previous ecommerce object first
  pushToDataLayer({
    event: null,
    ecommerce: null,
  });

  // Then push the new ecommerce data
  pushToDataLayer({
    event: "view_item",
    ecommerce: {
      currency: "CAD",
      value: product.price || 0,
      items: [
        {
          item_id: product.id.toString(),
          item_name: product.name,
          price: product.price || 0,
          item_category: product.category || "",
          quantity: 1,
        },
      ],
    },
  });

  // Track Meta Pixel ViewContent event
  trackMetaPixelEvent("ViewContent", {
    content_ids: [product.id.toString()],
    content_name: product.name,
    content_category: product.category || "",
    content_type: "product",
    value: product.price || 0,
    currency: "CAD",
  });
}

/**
 * Track an add to cart event
 * @param item The item being added to the cart
 * @param quantity The quantity being added
 */
export function trackAddToCart(
  item: {
    id: number | string;
    name: string;
    price?: number;
    category?: string;
  },
  quantity: number = 1
): void {
  // Clear previous ecommerce object first
  pushToDataLayer({
    event: null,
    ecommerce: null,
  });

  // Then push the new ecommerce data
  pushToDataLayer({
    event: "add_to_cart",
    ecommerce: {
      currency: "CAD",
      value: (item.price || 0) * quantity,
      items: [
        {
          item_id: item.id.toString(),
          item_name: item.name,
          price: item.price || 0,
          item_category: item.category || "",
          quantity: quantity,
        },
      ],
    },
  });

  // Track Meta Pixel AddToCart event
  trackMetaPixelEvent("AddToCart", {
    content_ids: [item.id.toString()],
    content_name: item.name,
    content_category: item.category || "",
    content_type: "product",
    value: (item.price || 0) * quantity,
    currency: "CAD",
    contents: [
      {
        id: item.id.toString(),
        quantity: quantity,
        item_price: item.price || 0,
      },
    ],
  });
}

/**
 * Track an initiate checkout event
 * @param cartItems The items in the cart
 * @param totalValue The total value of the cart
 */
export function trackInitiateCheckout(
  cartItems: Array<{
    id: number | string;
    name: string;
    price: number;
    quantity: number;
    category?: string;
  }>,
  totalValue: number
): void {
  // Clear previous ecommerce object first
  pushToDataLayer({
    event: null,
    ecommerce: null,
  });

  // Track Meta Pixel InitiateCheckout event
  trackMetaPixelEvent("InitiateCheckout", {
    content_ids: cartItems.map((item) => item.id.toString()),
    content_type: "product",
    value: totalValue,
    currency: "CAD",
    contents: cartItems.map((item) => ({
      id: item.id.toString(),
      quantity: item.quantity,
      item_price: item.price,
    })),
    num_items: cartItems.reduce((total, item) => total + item.quantity, 0),
  });
}

/**
 * Track a meal subscription event (weekly or bi-weekly)
 * @param subscriptionType The type of subscription ("weekly" or "biweekly")
 * @param orderSchedule The scheduled date and time
 * @param estimatedValue The estimated value of the subscription (optional)
 */
export function trackMealSubscription(
  subscriptionType: "weekly" | "biweekly",
  orderSchedule: { date: string; time: string },
  estimatedValue: number = 0
): void {
  // Track Meta Pixel Subscribe event with subscription details
  trackMetaPixelEvent("Subscribe", {
    value: estimatedValue,
    currency: "CAD",
    predicted_ltv:
      subscriptionType === "weekly" ? estimatedValue * 4 : estimatedValue * 2, // Estimate 4 weeks or 2 bi-weekly deliveries
    subscription_id: `${subscriptionType}_${orderSchedule.date}`,
    subscription_type: subscriptionType === "weekly" ? "weekly" : "bi-weekly",
  });
}

/**
 * Initialize analytics
 * Call this function once when the app initializes
 */
export function initializeAnalytics(): void {
  // Push any initial configuration to the dataLayer
  pushToDataLayer({
    event: "initialize_analytics",
    app_version: "1.0.0",
  });
}
