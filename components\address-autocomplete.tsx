"use client"

import { useState, useEffect, useRef } from "react"
import { useGoogleMaps } from "@/hooks/use-google-maps"
import { useCart } from "@/context/cart-context"
import { LocationPinIcon } from "./icons/location-pin-icon"
import { validateDeliveryAddress } from "@/services/address-validation-api"
import { useToast } from "@/context/toast-context"

// Fix Google Maps types by adding proper type definitions

// Add these type definitions at the top of the file after the imports:
declare global {
  interface Window {
    google: {
      maps: {
        places: {
          Autocomplete: new (
            input: HTMLInputElement,
            options?: {
              types?: string[]
              componentRestrictions?: { country: string }
            },
          ) => {
            addListener: (event: string, callback: () => void) => void
            getPlace: () => {
              formatted_address?: string
              geometry?: {
                location: {
                  lat: () => number
                  lng: () => number
                }
              }
              address_components?: Array<{
                long_name: string
                short_name: string
                types: string[]
              }>
            }
          }
        }
      }
    }
  }
}

type AddressAutocompleteProps = {
  onAddressSelect: (address: string) => void
  placeholder?: string
  value?: string
  hasError?: boolean
}

export default function AddressAutocomplete({
  onAddressSelect,
  placeholder = "Enter delivery address",
  value = "",
  hasError = false,
}: AddressAutocompleteProps) {
  const [inputValue, setInputValue] = useState(value)
  const [isValidating, setIsValidating] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)
  const autocompleteRef = useRef<any>(null)
  const { isLoaded, error } = useGoogleMaps()
  const { setAddressDetails, setUserLocation, addressDetails, userLocation, setAddressError } = useCart()
  const { showToast } = useToast()
  const pathname = typeof window !== "undefined" ? window.location.pathname : "/"
  const isMobile = typeof window !== "undefined" && window.innerWidth < 768
  const isHomePage = pathname === "/"

  // Initialize with value if provided
  useEffect(() => {
    if (value) {
      setInputValue(value)
    }
  }, [value])

  // Initialize autocomplete when script is loaded
  useEffect(() => {
    if (isLoaded && inputRef.current && window.google?.maps?.places) {
      try {
        autocompleteRef.current = new window.google.maps.places.Autocomplete(inputRef.current, {
          types: ["address"],
          componentRestrictions: { country: "ca" }, // Restrict to Canada, change as needed
        })

        autocompleteRef.current.addListener("place_changed", async () => {
          const place = autocompleteRef.current.getPlace()
          if (place && place.formatted_address) {
            setInputValue(place.formatted_address)
            onAddressSelect(place.formatted_address)

            // Extract address components
            const addressDetails = {
              address: place.formatted_address || "",
              city: "",
              appartment: "",
              area: "",
              country: "",
              postalCode: "",
            }

            // Extract location
            let userLocationData = { lat: "", lng: "" }
            if (place.geometry && place.geometry.location) {
              userLocationData = {
                lat: place.geometry.location.lat().toString(),
                lng: place.geometry.location.lng().toString(),
              }
              setUserLocation(userLocationData)
            }

            // Parse address components
            if (place.address_components) {
              for (const component of place.address_components) {
                const types = component.types

                if (types.includes("locality") || types.includes("administrative_area_level_3")) {
                  addressDetails.city = component.long_name
                }

                if (types.includes("route")) {
                  addressDetails.area = component.long_name
                }

                if (types.includes("country")) {
                  addressDetails.country = component.long_name
                }

                if (types.includes("postal_code")) {
                  addressDetails.postalCode = component.long_name
                }
              }
            }

            // Update address details in context
            setAddressDetails(addressDetails)

            // Validate the address if we have location data
            if (userLocationData.lat && userLocationData.lng) {
              setIsValidating(true)
              try {
                const validationResult = await validateDeliveryAddress(userLocationData, addressDetails)

                if (!validationResult.isValid) {
                  setAddressError(validationResult.message || "This address is outside our delivery area.")
                } else {
                  setAddressError(null)
                }
              } catch (error) {
                console.error("Address validation error:", error)
              } finally {
                setIsValidating(false)
              }
            }
          }
        })
      } catch (e) {
        console.error("Error initializing Google Places Autocomplete:", e)
      }
    }
  }, [isLoaded, onAddressSelect, setAddressDetails, setUserLocation, setAddressError, showToast])

  // Fallback to regular input if there's an error
  if (error) {
    console.warn("Google Maps script error:", error)
    // Fallback to regular input
    return (
      <div className="relative">
        <LocationPinIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-black" size={20} />
        <input
          type="text"
          placeholder={placeholder}
          className={`w-full p-2 pl-10 border ${hasError ? "border-red-500" : "border-gray-300"} ${
            isHomePage && isMobile ? "rounded-none" : "rounded-md"
          } text-black`}
          style={{ fontSize: "16px" }}
          value={inputValue}
          onChange={(e) => {
            setInputValue(e.target.value)
            if (e.target.value) {
              onAddressSelect(e.target.value)
            } else {
              onAddressSelect("")
            }
          }}
        />
      </div>
    )
  }

  return (
    <div className="relative">
      <LocationPinIcon className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-500" size={20} />
      <input
        ref={inputRef}
        type="text"
        placeholder={placeholder}
        className={`w-full md:w-[377px] h-[62px] p-2 pl-10 border ${hasError ? "border-red-500" : "border-gray-300"} ${
          isHomePage && isMobile ? "rounded-none" : "rounded-md"
        } text-black`}
        style={{ fontSize: "16px" }}
        value={inputValue}
        onChange={(e) => {
          setInputValue(e.target.value)
          if (e.target.value === "") {
            onAddressSelect("")
          }
        }}
        disabled={isValidating}
      />
      {isValidating && (
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          <div className="bg-white px-3 py-2 -ml-2 rounded-l-md">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-gray-900"></div>
          </div>
        </div>
      )}
    </div>
  )
}
