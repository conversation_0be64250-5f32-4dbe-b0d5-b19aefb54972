import { Skeleton } from "@/components/ui/skeleton"

export default function HomePageSkeleton() {
  return (
    <main className="min-h-screen flex flex-col">
      {/* Hero Section Skeleton - Full width */}
      <section className="relative w-full">
        {/* Mobile Hero Skeleton */}
        <div className="md:hidden bg-yellow-400 w-full pb-64">
          <div className="p-4">
            <Skeleton className="h-8 w-48 mb-4" />
            <div className="space-y-3">
              <Skeleton className="h-12 w-full" />
              <div className="flex gap-2">
                <Skeleton className="h-12 flex-1" />
                <Skeleton className="h-12 w-20" />
              </div>
            </div>
          </div>
          <div className="absolute bottom-0 right-0 w-full">
            <Skeleton className="h-64 w-full" />
          </div>
        </div>

        {/* Desktop Hero Skeleton */}
        <div className="hidden md:block w-full h-[500px] lg:h-[600px] bg-gray-200 animate-pulse">
          <div className="absolute inset-0 flex flex-col items-start justify-start">
            <div className="p-4 md:p-8 mt-4">
              <div className="max-w-md">
                <Skeleton className="h-8 w-48 mb-4" />
                <Skeleton className="h-10 w-40 mb-4" />
                <div className="space-y-3">
                  <Skeleton className="h-12 w-full" />
                  <div className="flex gap-2">
                    <Skeleton className="h-12 flex-1" />
                    <Skeleton className="h-12 w-20" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* How It Works Section Skeleton */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4 max-w-7xl">
          <Skeleton className="h-8 w-64 mx-auto mb-12" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex flex-col items-center text-center">
                <Skeleton className="w-16 h-16 rounded-full mb-4" />
                <Skeleton className="h-6 w-40" />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Featured Menu Section Skeleton */}
      <section className="py-16 bg-gray-50">
        <div className="container mx-auto px-4 max-w-7xl">
          <Skeleton className="h-8 w-64 mx-auto mb-12" />
          <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-x-4 gap-y-10 md:gap-x-6">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="flex flex-col items-center">
                <Skeleton className="w-full aspect-square mb-3" />
                <Skeleton className="h-6 w-3/4 mb-2" />
                <div className="flex justify-center mb-2">
                  {[...Array(5)].map((_, j) => (
                    <Skeleton key={j} className="w-5 h-5 rounded-full mx-0.5" />
                  ))}
                </div>
                <Skeleton className="h-5 w-1/2" />
              </div>
            ))}
          </div>
          <div className="flex justify-center mt-10">
            <Skeleton className="h-12 w-40" />
          </div>
        </div>
      </section>
    </main>
  )
}
