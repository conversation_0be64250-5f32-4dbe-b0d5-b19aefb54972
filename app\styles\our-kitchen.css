.copyright_footer {
  float: left;
  width: 100%;
  display: inline-block;
}

.static_banner_section {
  display: block;
  width: 100%;
  position: relative;
}

.static_Banner_slider {
  overflow: hidden;
  margin: 0px;
}

.static_Banner_slider .Banner {
  margin: 0px !important;
}

.static_Banner figure {
  float: left;
  width: 100%;
  margin-bottom: 0px;
  position: relative;
}

.static_Banner figure img {
  float: left;
  width: 100%;
}

.working_process_parent {
  display: block;
  width: 100%;
  position: relative;
  float: left;
}
img.custom_11_ab_1 {
  position: absolute;
  bottom: 0px;
  right: -82px;
  max-width: 1100px;
}

.working_process_heading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 49px;
  flex-direction: column;
}

.working_process_heading h2 {
  margin: 0px;
  font-family: Anton;
  font-size: 77px;
  font-weight: 400 !important;
  line-height: 88.09px;
  letter-spacing: 0.22em;
  text-align: center;
  text-transform: uppercase;
  color: #000000;
}

.working_process_heading small {
  margin: 0px;
  font-family: Poppins;
  font-size: 34px;
  font-weight: 400;
  line-height: 51px;
  letter-spacing: 0.39em;
  text-align: center;
  text-transform: uppercase;
  color: #000000;
}

.working_process_main {
  display: grid;
  width: 100%;
  margin-bottom: 131px;
}

.heading_box {
  position: relative;
}

.custom_container {
  max-width: 1140px;
  margin: 0 auto;
}

.custom_11 {
  display: grid;
  grid-template-columns: 1.95fr 1fr;
  grid-template-rows: auto;
  position: relative;
  z-index: 1;
  width: 100%;
  align-items: center;
  height: 100%;
}

.custom_11.change_side_main {
  display: grid;
  grid-template-columns: 1.6fr 0.98fr;
  grid-template-rows: auto;
  position: relative;
  z-index: 1;
  width: 100%;
  align-items: center;
  height: 100%;
}

.custom_11 figure {
  margin-bottom: 0px;
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
}

.custom_11 figure img {
  object-fit: cover;
  width: 100%;
}

.responsive_one {
  display: none !important;
}

.custom_11_body_main {
  align-items: flex-start;
  display: flex;
  width: 100%;
  padding: 0px 33px 60px 66px;
  height: 100%;
}

.custom_11_body_main.main_two {
  padding: 53px 15px 23px 38px;
}

.custom_11_body_main.change_side {
  align-items: center;
  display: flex;
  width: 100%;
  padding: 23px 38px 23px 50px;
}

.custom_11_body {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.custom_11_heading {
  display: flex;
  width: 100%;
  flex-direction: column;
  justify-content: center;
}

.process_heading_parent figure {
  display: none;
}

.custom_11_heading h3 {
  font-family: Anton;
  font-size: 77px;
  font-weight: 400;
  line-height: 88.09px;
  padding-bottom: 13px;
  margin: 0px;
  color: #000000;
  text-transform: uppercase;
}

.custom_11_heading p {
  font-family: Poppins;
  font-size: 23px;
  font-weight: 400;
  line-height: 31.01px;
  margin: 0px;
  color: #000000;
}

p.join_text {
  font-size: 20px;
  line-height: 30px;
}

.section_11_single_box_icon {
  right: -52px;
  top: -82px;
  position: absolute;
}

.section_11_single_box_icon img {
  width: 92px !important;
}

.section_11_single_box_icon.first_absolute {
  position: absolute;
  left: -38px;
  top: -89px;
  right: unset;
}

.section_11_single_box_icon.first_absolute img {
  width: 62px !important;
}

.section_11_single_box_icon.third_absolute {
  position: absolute;
  left: -40px;
  top: -61px;
}

.section_11_single_box_icon.third_absolute img {
  width: 80px !important;
}

.cat_btn a {
  font-family: Poppins;
  font-size: 16px;
  font-weight: 600;
  line-height: 24px;
  letter-spacing: 0em;
  border: 1px solid #000000;
  width: 238px;
  height: 55px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #000000;
  border-radius: 50px;
  color: #fff;
}

/*  */
.custom_13_parent {
  display: grid;
  grid-template-columns: 1.6fr 0.82fr;
  grid-template-rows: auto;
  position: relative;
  z-index: 1;
  width: 100%;
  align-items: center;
  height: 100%;
}

.custom_13 {
  padding: 15px 40px;
}

.custom_13_dek_img {
  margin-bottom: 0px;
  height: 817px;
  background: linear-gradient(179.98deg, #ffbe30 0.02%, #ffa800 156.21%);
  position: relative;
  width: 100%;
}

.custom_13_dek_img img {
  position: absolute;
  top: 80px;
  left: -46px;
  max-width: 419px;
}

.custom_13_content {
  display: grid;
  width: 100%;
}

.custom_13_content h2 {
  font-family: Anton;
  font-size: 77px;
  font-weight: 400;
  line-height: 88.09px;
  letter-spacing: 0em;
  padding-bottom: 67px;
  margin: 0px;
  color: #000000;
  text-transform: uppercase;
}

.custom_13_content ul {
  display: grid;
  width: 100%;
  gap: 20px 0px;
  padding: 0px 0px 0px 17px;
}

.custom_13_content ul li {
  font-family: Poppins;
  font-size: 23px;
  font-weight: 400;
  line-height: 33px;
  letter-spacing: 0em;
  margin: 0px;
  color: #000000;
  width: 100%;
  list-style: disc;
}

.custom_13_content ul li::marker {
  color: #ffa800 !important;
  font-size: 22px;
}

.custom_13_dek_img {
  display: block;
}

.custom_13_mob_img {
  display: none;
}

@media (max-width: 1400px) {
  .custom_13_parent {
    grid-template-columns: 1.2fr 0.55fr;
  }

  .custom_13_content h2 {
    font-size: 61px;
    line-height: 80.09px;
    padding-bottom: 45px;
  }

  .custom_13_content ul li {
    font-size: 20px;
    line-height: 29.01px;
  }

  .custom_13_dek_img {
    height: 600px;
  }

  .custom_13_dek_img img {
    left: -32px;
    max-width: 296px;
  }
}

@media (max-width: 1140px) {
  .custom_13 {
    padding: 15px 20px;
  }
}

@media (max-width: 1024px) {
  .custom_13_parent {
    grid-template-columns: 1.1fr 0.55fr;
  }

  .custom_13_content h2 {
    font-size: 40px;
    line-height: 50px;
    padding-bottom: 32px;
  }

  .custom_13_content ul li {
    font-size: 16px;
    line-height: 24.01px;
  }
}

@media (max-width: 768px) {
  .custom_13_parent {
    padding: 30px 0px;
    display: flex;
    flex-direction: column-reverse;
    gap: 0px;
  }

  .custom_13 {
    padding: 30px 30px 0px 30px !important;
  }

  .custom_13_content h2 {
    font-size: 32px;
    line-height: 36px;
  }

  .custom_13_content ul li {
    font-size: 15px;
    line-height: 24px;
  }

  .custom_13_content ul {
    gap: 10px 0px;
  }

  .custom_13_dek_img {
    display: none;
  }

  .custom_13_mob_img {
    display: block;
    width: 100%;
  }

  .custom_13_mob_img img {
    width: 100%;
  }
}

/*  */
.custom_12 {
  display: grid;
  grid-template-columns: 1.95fr 1fr;
  grid-template-rows: auto;
  position: relative;
  z-index: 1;
  width: 100%;
  align-items: center;
  height: 100%;
}

.custom_12_ab_1 {
  top: -22px;
  right: 103px;
  position: absolute;
  width: 150px;
}

.custom_12_body {
  display: grid;
  width: 100%;
  height: 100%;
  gap: 20px;
  align-items: flex-start;
  background: linear-gradient(180deg, #ffc949 0%, #fdac1c 100%);
  border-bottom-right-radius: 252px;
}

.custom_12_heading {
  display: grid;
  gap: 30px;
  padding: 46px 50px 71px 60px;
}

.custom_12_heading h3 {
  font-family: Anton;
  font-size: 90px;
  font-weight: 400;
  line-height: 102.97px;
  letter-spacing: 0.02em;
  text-align: left;
  color: #000000;
  text-transform: uppercase;
}

.custom_12_heading p {
  font-family: Poppins;
  font-size: 23px;
  font-weight: 400;
  line-height: 34.5px;
  text-align: left;
  color: #000000;
}

.custom_12_body figure img {
  width: 100%;
}
.custom_12_heading_two {
  display: flex;
  padding: 74px 52px 70px 52px;
  background: #e7e7e7;
  width: 100%;
  height: 75%;
  flex-direction: column;
  justify-content: end;
}

.custom_12_heading_two h3 {
  font-family: Anton;
  font-size: 90px;
  font-weight: 400;
  line-height: 102.97px;
  letter-spacing: 0.02em;
  text-align: left;
  color: #000;
  margin-bottom: 30px;
  text-transform: uppercase;
}

.custom_12_heading_two p {
  font-family: Poppins;
  font-size: 23px;
  font-weight: 400;
  line-height: 34.5px;
  text-align: left;
  color: #000;
}

@media (max-width: 1400px) {
  .custom_12 {
    grid-template-columns: 2.18fr 1fr;
  }
  .custom_12_heading_two {
    padding: 148px 52px 70px 52px;
  }
  .custom_12_heading h3 {
    font-size: 61px;
    line-height: 81.97px;
  }

  .custom_12_heading p {
    font-size: 20px;
    line-height: 29.5px;
  }

  .custom_12_heading_two h3 {
    font-size: 61px;
    line-height: 81.97px;
  }

  .custom_12_heading_two p {
    font-size: 20px;
    line-height: 29.5px;
  }

  img.custom_12_ab_1 {
    right: 48px;
  }
}

@media (max-width: 1140px) {
  .custom_12_heading {
    padding: 23px 20px 23px 20px;
  }
  .custom_12_heading_two {
    padding: 70px 20px 23px 20px;
  }

  img.custom_12_ab_1 {
    display: none;
  }
}

@media (max-width: 1024px) {
  .custom_12 {
    grid-template-columns: 2fr 1fr;
  }

  .custom_12_heading h3 {
    font-size: 40px;
    line-height: 50px;
  }

  .custom_12_heading p {
    font-size: 16px;
    line-height: 24.01px;
  }

  .custom_12_heading_two h3 {
    font-size: 40px;
    line-height: 70px;
  }

  .custom_12_heading_two p {
    font-size: 16px;
    line-height: 24.01px;
  }
}

@media (max-width: 768px) {
  .custom_12 {
    grid-template-columns: 1fr;
  }
  .custom_12_heading_two {
    padding: 23px 20px 23px 20px;
  }
}

/*  */
@media (max-width: 1400px) {
  img.custom_11_ab_1 {
    right: -82px;
    max-width: 100%;
  }
  .custom_11 {
    display: grid;
    grid-template-columns: 2.2fr 1fr;
  }

  .custom_11_heading h3 {
    font-size: 61px;
    line-height: 80.09px;
  }

  .custom_11_heading p {
    font-size: 20px;
    line-height: 29.01px;
  }

  .heading_box h3 {
    text-align: left;
  }

  .custom_11_body_main.main_two .heading_box h3 {
    text-align: left;
  }
}

@media (max-width: 1140px) {
  .ab_one img {
    max-width: 100px !important;
  }

  .custom_11_heading h3 {
    font-size: 52px;
    line-height: 66px;
  }

  .custom_11_heading p {
    font-size: 18px;
    line-height: 27.01px;
  }

  .working_process_heading h2 {
    font-size: 41px;
    line-height: 62px;
  }

  .working_process_heading small {
    font-size: 18px;
    line-height: 28px;
  }
}

@media (max-width: 1024px) {
  .custom_11_body_main {
    padding: 0px 15px 23px 30px;
  }
  .custom_11_fig {
    border-top-right-radius: 270px !important;
  }
  img.custom_11_ab_1 {
    right: -54px;
  }
  .custom_11_body_main.main_two {
    padding: 35px 15px 23px 30px;
  }

  .custom_11_heading p {
    font-size: 16px;
    line-height: 24.01px;
  }

  .working_process_heading h2 {
    font-size: 45px;
    line-height: 58px;
  }

  .working_process_heading small {
    font-size: 20px;
    line-height: 28px;
  }

  .custom_11_heading h3 {
    font-size: 40px;
    line-height: 50px;
    padding-bottom: 15px;
  }

  .ab_one img {
    max-width: 74px !important;
  }

  .custom_11 {
    display: grid;
    grid-template-columns: 2fr 1fr;
  }
}

@media (max-width: 768px) {
  figure.ab_one {
    display: none;
  }
  .custom_11_fig {
    border-top-right-radius: 135px !important;
  }
  figure.responsive_one {
    display: flex !important;
  }

  .custom_11_heading.first_sec_heading {
    display: grid;
    grid-template-columns: 1fr 1fr;
  }

  figure.responsive_one img {
    max-width: 100px;
    margin: 0 auto;
    object-fit: contain;
  }

  .process_heading_parent {
    display: flex;
    align-items: center;
  }

  .process_heading_parent img {
    width: 50px;
  }

  .process_heading_parent figure {
    display: flex;
    margin-right: 10px;
  }

  .custom_11 {
    background: transparent !important;
  }

  .custom_11_body_main.main_two {
    padding: 0px 30px 23px 30px !important;
  }

  .working_process_heading h2 {
    font-size: 29px;
    line-height: 32px;
    letter-spacing: 0.1em;
    padding-bottom: 3px;
  }

  .process_heading_parent img {
    width: 35px;
  }

  .working_process_heading small {
    font-size: 15px;
    line-height: 16px;
    letter-spacing: 0.1em;
  }

  .custom_11_body_main {
    padding: 0px 30px 0px 30px !important;
  }

  .custom_11_heading h3 {
    font-size: 38px;
    line-height: 52px;
  }

  .custom_11 {
    grid-template-columns: 1fr;
    gap: 27px;
  }

  .custom_11_body {
    max-width: 100%;
  }

  .custom_11 figure img {
    object-fit: cover;
  }

  .section_11_single_box_icon {
    right: -23px;
    top: -12px;
  }

  .section_11_single_box_icon img {
    width: 28px !important;
  }

  .ab_one img {
    max-width: 58px !important;
  }

  .custom_11_heading h3 {
    font-size: 32px;
    line-height: 36px;
  }

  .custom_11_heading p {
    font-size: 15px;
    line-height: 24px;
  }
  img.custom_11_ab_1 {
    right: 0px;
    position: unset;
    padding: 30px 15px 0px 15px;
  }
}

@media (max-width: 540px) {
  .custom_11_heading.first_sec_heading {
    display: flex;
    flex-direction: column-reverse;
  }

  figure.responsive_one img {
    max-width: 40px;
    margin: 0px 0px 20px 36px;
    object-fit: contain !important;
  }

  .custom_11_body_main {
    padding: 0px 20px 0px 20px !important;
  }

  .custom_11_body_main.change_side {
    padding: 30px 20px 0px 20px !important;
  }

  .custom_11_body_main.main_two {
    padding: 0px 20px 30px 20px !important;
  }

  .process_heading_parent figure {
    display: none;
  }

  .working_process_heading h2 {
    font-size: 24px;
    line-height: 28px;
  }

  .working_process_heading small {
    font-size: 12px;
    line-height: 9px;
  }

  .working_process_heading {
    padding-bottom: 25px;
  }

  .custom_11_heading.first_sec_heading {
    display: flex;
    flex-direction: column-reverse;
  }
}

.cat_btn {
  margin-top: 15px;
}
.custom_image_sec {
  max-width: 164px;
}
.process_heading_custom {
  text-align: center;
}
.custom_11_fig {
  background: linear-gradient(180deg, #ffc949 0%, #fdac1c 100%);
  border-top-right-radius: 452px;
}
.custom_11_heading.first_sec_heading .ab_one {
  margin-bottom: 28px;
  justify-content: center;
}
.custom_11_heading.first_sec_heading .ab_one img {
  max-width: 164px;
}

.custom_sec_14 {
  display: block;
  width: 100%;
  padding: 0px 15px 90px 15px;
  float: left;
}
.custom_14_data_parent {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-bottom: 130px;
}
.custom_14_data_1 {
  display: grid;
  grid-template-columns: 2.5fr 1fr;
  align-items: center;
  padding-bottom: 22px;
}
.custom_14_data_1 strong {
  font-family: Anton;
  font-size: 160px;
  font-weight: 400;
  line-height: 137px;
  text-align: left;
  color: #000;
  text-transform: uppercase;
}
.custom_14_data_1 span {
  font-family: Poppins;
  font-size: 44px;
  font-weight: 400;
  line-height: 61px;
  letter-spacing: 0.04em;
  color: #000000;
  text-transform: uppercase;
}
.custom_14_data_2 {
  display: grid;
  grid-template-columns: 0.5fr 1fr;
  align-items: center;
  padding-bottom: 22px;
}
.custom_14_data_2 strong {
  font-family: Anton;
  font-size: 160px;
  font-weight: 400;
  line-height: 151px;
  text-align: left;
  color: #000;
}
.custom_14_data_2 span {
  font-family: Poppins;
  font-size: 44px;
  font-weight: 400;
  line-height: 54px;
  letter-spacing: 0.04em;
  text-align: left;
  text-transform: uppercase;
  max-width: 445px;
}
.custom_14_data_2 span small {
  font-weight: 800;
  text-transform: lowercase !important;
}
.custom_14_data_parent p {
  font-family: Poppins;
  font-size: 32px;
  font-weight: 400;
  line-height: 51px;
  letter-spacing: 0.04em;
  color: #000;
}
.custom_14_data_parent_2 {
  display: grid;
  grid-template-columns: 1fr 6px 5fr;
  gap: 30px 61px;
  align-items: center;
  background: linear-gradient(180deg, #ffc949 0%, #fdac1c 100%);
  padding: 30px 35px 30px 10px;
  border-bottom-right-radius: 252px;
}
.custom_14_data_parent_2 strong {
  font-family: Anton;
  font-size: 116px;
  font-weight: 400;
  line-height: 106.72px;
  letter-spacing: 0.02em;
  text-align: center;
  text-transform: uppercase;
}
.custom_14_data_parent_2 strong small {
  font-family: Anton;
  font-size: 168px;
  font-weight: 400;
  line-height: 154.56px;
  letter-spacing: 0.02em;
}
.custom_14_data_parent_2 p {
  font-family: Poppins;
  font-size: 23px;
  font-weight: 400;
  line-height: 28px;
  color: #000;
}
.custom_14_data_parent_2 span {
  border: 6px solid #000;
  height: 100%;
  display: flex;
}
@media (max-width: 1160px) {
  .working_process_main {
    margin-bottom: 90px;
  }

  .custom_14_data_parent {
    padding-bottom: 90px;
  }
  .custom_14_data_1 strong {
    font-size: 101px;
    line-height: 90px;
  }
  .custom_sec_14 > .custom_container {
    max-width: 768px;
  }
  .custom_14_data_1 span {
    font-size: 26px;
    line-height: 38px;
  }
  .custom_14_data_1 {
    grid-template-columns: 2fr 1fr;
  }
  .custom_14_data_2 strong {
    font-size: 101px;
    line-height: 90px;
  }
  .custom_14_data_2 span {
    font-size: 26px;
    line-height: 38px;
    max-width: 275px;
  }
  .custom_14_data_parent p {
    font-size: 24px;
    line-height: 33px;
  }
  .custom_14_data_parent_2 strong {
    font-size: 80px;
    line-height: 83px;
  }
  .custom_14_data_parent_2 strong small {
    font-size: 124px;
    line-height: 120px;
  }
  .custom_14_data_parent_2 {
    gap: 30px 40px;
  }
  .custom_14_data_parent_2 p {
    font-size: 16px;
    line-height: 25px;
  }
  .custom_12_body {
    border-bottom-right-radius: 90px;
  }
}
@media (max-width: 820px) {
  .custom_sec_14 > .custom_container {
    max-width: 320px;
  }
  .custom_sec_14 {
    padding: 0px 15px 60px 15px;
  }
  .working_process_main {
    margin-bottom: 40px;
  }
  .custom_14_data_2 {
    padding-bottom: 12px;
  }
  .custom_14_data_parent {
    padding-bottom: 30px;
  }
  .custom_14_data_1 {
    padding-bottom: 12px;
  }
  .custom_14_data_1 span {
    font-size: 14px;
    line-height: 18px;
  }
  .custom_14_data_1 strong {
    font-size: 38px;
    line-height: 38px;
  }
  .custom_14_data_2 strong {
    font-size: 38px;
    line-height: 38px;
  }
  .custom_14_data_2 span {
    font-size: 14px;
    line-height: 18px;
  }
  .custom_14_data_2 {
    grid-template-columns: 0.45fr 1fr;
  }
  .custom_14_data_parent p {
    font-size: 16px;
    line-height: 20px;
  }
  .custom_14_data_parent_2 strong {
    font-size: 50px;
    line-height: 60px;
    display: flex;
    flex-direction: column;
  }
  .custom_14_data_parent_2 strong small {
    font-size: 78px;
    line-height: 65px;
  }
  .custom_14_data_parent_2 {
    grid-template-columns: 1fr;
    gap: 20px 40px;
    border-bottom-right-radius: 90px;
    padding: 30px 15px;
  }
  .custom_14_data_parent_2 p {
    font-size: 13px;
    line-height: 23px;
  }
  .custom_14_data_parent_2 span {
    border: 3px solid #000;
  }
  .custom_14_data_1 {
    grid-template-columns: 1.6fr 1fr;
  }
}
