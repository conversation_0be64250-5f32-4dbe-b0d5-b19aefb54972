"use client";
import Image from "next/image";
import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useToast } from "@/context/toast-context";
import { useCart } from "@/context/cart-context";
import { useBusiness } from "@/context/business-context";
import { validateDeliveryAddress } from "@/services/address-validation-api";
import type { ProductReview } from "@/types/product";
import ReviewFormModal from "./review-form-modal";
import StarRating from "./star-rating";

// Simple function to extract and clean description content
function extractDescription(description = "") {
  // Remove the custom tags but keep their content
  const cleanedDesc = description
    .replace(/<short_desc>(.*?)<\/short_desc>/gs, "")
    .replace(/<long_desc>(.*?)<\/long_desc>/gs, "$1")
    // Remove any Bootstrap-specific classes
    .replace(/class="[^"]*"/g, "")
    // Remove data attributes
    .replace(/data-[^=]*="[^"]*"/g, "")
    .trim();

  return cleanedDesc;
}

// Function to calculate average rating
function calculateAverageRating(reviews?: ProductReview[]): number {
  if (!reviews || reviews.length === 0) return 0;
  const sum = reviews.reduce((acc, review) => acc + review.rating, 0);
  return sum / reviews.length;
}

export type ProductDetailContentProps = {
  product: any;
  onReviewSubmit?: (reviewData: {
    rating: number;
    name: string;
    comment: string;
    productId: string;
  }) => Promise<void> | void;
  className?: string;
  onModalClose?: () => void;
};

export default function ProductDetailContent({
  product,
  onReviewSubmit,
  className = "",
  onModalClose,
}: ProductDetailContentProps) {
  const [isReviewModalOpen, setIsReviewModalOpen] = useState(false);
  const router = useRouter();
  const { showToast } = useToast();
  const {
    orderType,
    deliveryTiming,
    deliveryAddress,
    addressDetails,
    userLocation,
    addressError,
    setAddressError,
    orderSchedule,
    scheduledDateTime,
  } = useCart();
  const { selectedBranch } = useBusiness();

  // Validation function similar to the one used on home page
  const validateAndProceed = async () => {
    try {
      // Validate based on order type
      if (orderType === "delivery") {
        if (!deliveryAddress) {
          showToast({
            message:
              "Almost there! Just enter your address to unlock our menu.",
            type: "error",
            duration: 5000,
          });
          router.push("/");
          return;
        }

        // Validate the delivery address with the API
        if (userLocation.lat && userLocation.lng) {
          try {
            const validationResult = await validateDeliveryAddress(
              userLocation,
              addressDetails
            );

            if (!validationResult.isValid) {
              setAddressError(
                validationResult.message ||
                  "This address is outside our delivery area."
              );
              showToast({
                message: "Please enter a valid delivery address.",
                type: "error",
                duration: 5000,
              });
              router.push("/");
              return;
            }
          } catch (error) {
            console.error("Address validation error:", error);
            setAddressError(
              "Unable to validate delivery address. Please try again."
            );
            showToast({
              message: "Unable to validate delivery address. Please try again.",
              type: "error",
              duration: 5000,
            });
            router.push("/");
            return;
          }
        }
      } else if (orderType === "pickup") {
        if (!selectedBranch) {
          showToast({
            message:
              "Almost there! Just select your pickup location to unlock our menu.",
            type: "error",
            duration: 5000,
          });
          router.push("/");
          return;
        }
      }

      console.log("deliveryTiming = ", deliveryTiming);

      // Validate delivery timing
      if (!deliveryTiming) {
        showToast({
          message: "Please select a delivery/pickup timing",
          type: "warning",
          duration: 4000,
        });
        router.push("/");
        return;
      }

      // Check if scheduled delivery option is selected
      if (
        deliveryTiming === "later" ||
        deliveryTiming === "weekly" ||
        deliveryTiming === "biweekly"
      ) {
        // Check if schedule date and time are set
        if (!orderSchedule || !orderSchedule.date || !orderSchedule.time) {
          // Show appropriate message for scheduling
          const scheduleMessage =
            deliveryTiming === "later"
              ? `Almost there! Just schedule your ${
                  orderType === "delivery" ? "delivery" : "pickup"
                } time to unlock our menu.`
              : deliveryTiming === "weekly"
              ? "Almost there! Just set up your weekly subscription to unlock our menu."
              : "Almost there! Just set up your bi-weekly subscription to unlock our menu.";

          showToast({
            message: scheduleMessage,
            type: "error",
            duration: 5000,
          });
          router.push("/");
          return;
        }
      }

      // If all validations pass, redirect to menu page
      router.push("/menu");
    } catch (error) {
      console.error("Error during validation:", error);
      showToast({
        message: "An error occurred. Please try again",
        type: "error",
        duration: 4000,
      });
      router.push("/");
    }
  };

  useEffect(() => {
    // Function to set up accordion functionality
    const setupAccordion = () => {
      const long_data = document.querySelectorAll(".log_des_acc_header");

      // First remove any existing event listeners to prevent duplicates
      long_data.forEach((header) => {
        const oldHeader = header.cloneNode(true);
        header.parentNode.replaceChild(oldHeader, header);
      });

      // Now add fresh event listeners
      document.querySelectorAll(".log_des_acc_header").forEach((header) => {
        header.addEventListener("click", function (e) {
          e.preventDefault();
          const targetId = this.getAttribute("data-target");
          if (!targetId) return;

          const targetElement = document.querySelector(targetId);
          const arrow = this.querySelector(".detail_arrow");

          if (!targetElement || !arrow) return;

          // Toggle the current section
          if (targetElement.classList.contains("newshow")) {
            targetElement.classList.remove("newshow");
            arrow.classList.remove("rotate");
          } else {
            // Close all other sections
            document.querySelectorAll(".newcollapse").forEach((collapse) => {
              collapse.classList.remove("newshow");
            });

            document.querySelectorAll(".detail_arrow").forEach((a) => {
              a.classList.remove("rotate");
            });

            // Open this section
            targetElement.classList.add("newshow");
            arrow.classList.add("rotate");
          }
        });
      });

      // Add the exact JS code as provided
      document
        .getElementById("toggle_desc_sub_heading")
        ?.addEventListener("click", () => {
          document
            .getElementById("toggle_desc_sub_paragraph")
            ?.classList.toggle("sub_desc_toggle_hidden");
        });
    };

    // Set up accordion with a slight delay to ensure DOM is ready
    const timer = setTimeout(() => {
      setupAccordion();
    }, 100);

    return () => {
      clearTimeout(timer);
    };
  }, [isReviewModalOpen, product]); // Add isReviewModalOpen as a dependency so it re-runs when modal state changes

  const handleSubmitReview = async (reviewData: {
    rating: number;
    name: string;
    comment: string;
    productId: string;
  }) => {
    try {
      if (onReviewSubmit) {
        await onReviewSubmit(reviewData);
      }

      // Close the review modal
      setIsReviewModalOpen(false);
    } catch (error) {
      console.error("Error handling review submission:", error);
    }
  };

  if (!product) return null;

  return (
    <div className={`pro_model_scroll ${className}`}>
      {/* Product header section - Keep this with Tailwind */}
      <div className="flex flex-col md:flex-row gap-8 mb-8 pop_box_1">
        {/* Product image */}
        <div className="w-full md:w-1/2">
          <div className="relative aspect-square rounded-lg overflow-hidden">
            <Image
              src={product.image || "/placeholder.svg"}
              alt={product.name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, 50vw"
              priority
            />
          </div>
        </div>

        {/* Product info */}
        <div className="w-full md:w-1/2 product_detail_page_headings">
          <div>
            <h2 className="text-3xl font-bold mb-2 uppercase">
              {product.name}
            </h2>

            <div className="pop_header">
              {/* Ratings */}
              <div className="flex items-center">
                <StarRating rating={product.rating} starSize={20} />
              </div>

              {/* Price */}
              <h6 className="text-lg font-bold">
                CAD {product.price.toFixed(2)}
              </h6>
            </div>
          </div>

          {/* ORDER NOW Link */}
          <div className="mt-7 md:mt-16">
            <a
              onClick={async (e) => {
                e.preventDefault();

                // Close modal if we're in a modal context
                if (onModalClose) {
                  onModalClose();
                }

                // Use the validation function instead of just showing a message
                await validateAndProceed();
              }}
              href="/"
              className="font-poppins text-[18px] md:text-[20px] font-normal leading-[20px] tracking-[0em] inline-flex items-center justify-center px-6 py-3 rounded-[2px] capitalize bg-black text-white cursor-pointer shadow-[0px_48px_100px_0px_rgba(17,12,46,0.15)] hover:shadow-lg transition-shadow duration-200 !no-underline"
            >
              ORDER NOW
            </a>
          </div>
        </div>
      </div>

      {/* Product Description - This is where we apply the anti-Tailwind scope */}
      <div
        dangerouslySetInnerHTML={{
          __html: extractDescription(product.description),
        }}
      />

      {/* Review Section - Using Tailwind for this part */}
      <div className="log_des_rating_box">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-normal m-0 rating-overview-heading">
            RATING OVERVIEW
          </h2>
          <button
            className="add_review_btn"
            onClick={(e) => {
              e.stopPropagation();
              setIsReviewModalOpen(true);
            }}
          >
            WRITE A REVIEW
          </button>
        </div>

        {product.reviews && product.reviews.length > 0 ? (
          <>
            <h4 className="overall-rating-heading m-0">Overall Rating</h4>
            <div className="flex items-center gap-4 mb-6">
              <span className="text-7xl font-bold leading-none main_rating">
                {product.rating}
              </span>
              <div>
                <div className="flex mb-1">
                  <StarRating rating={product.rating} starSize={24} />
                </div>
                <p className="text-gray-600 m-0">
                  Based on {product.reviews.length}{" "}
                  {product.reviews.length === 1 ? "review" : "reviews"}
                </p>
              </div>
            </div>

            <h3 className="reviews-heading">REVIEWS</h3>

            <div className="log_des_review_list">
              {product.reviews
                .slice()
                .reverse()
                .map((review) => (
                  <div
                    key={review.id}
                    className="flex items-start gap-4 log_des_single_review"
                  >
                    <figure>
                      <img src="https://static.tossdown.com/site/0b4566ce-b249-448f-883b-f9f2e0aa5a96.webp" />
                      <figcaption>
                        <h5> {review.user_name} </h5>
                        <span> {review.date} </span>
                      </figcaption>
                    </figure>
                    <div className="log_des_single_review_data">
                      <div className="log_des_single_review_stars">
                        <StarRating rating={review.rating} starSize={20} />
                      </div>
                      <p> {review.comment} </p>
                    </div>
                  </div>
                ))}
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-gray-500 mb-4">
              No reviews yet. Be the first to review this product!
            </p>
          </div>
        )}
      </div>

      {/* Review Form Modal */}
      <ReviewFormModal
        isOpen={isReviewModalOpen}
        onClose={() => setIsReviewModalOpen(false)}
        productId={product?.id || product?.menu_item_id || ""}
        productName={product?.name || ""}
        onSubmitReview={handleSubmitReview}
      />
    </div>
  );
}
