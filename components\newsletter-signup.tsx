"use client"
import dynamic from "next/dynamic"
import { testimonials, testimonialAssets, type Testimonial } from "../data/testimonials"
import { useState, useEffect } from "react"

// Dynamically import Slider with no SSR to avoid hydration issues
const Slider = dynamic(() => import("react-slick"), { ssr: false })

export default function NewsletterSignup() {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  // Function to render stars based on rating
  const renderStars = (rating: number) => {
    const stars = []
    const fullStars = Math.floor(rating)
    const hasHalfStar = rating % 1 !== 0

    // Add full stars
    for (let i = 0; i < fullStars; i++) {
      stars.push(<i key={`full-${i}`} className="fas fa-star"></i>)
    }

    // Add half star if needed
    if (hasHalfStar) {
      stars.push(<i key="half" className="fad fa-star-half-alt"></i>)
    }

    // Add empty stars to make total of 5
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0)
    for (let i = 0; i < emptyStars; i++) {
      stars.push(<i key={`empty-${i}`} className="far fa-star"></i>)
    }

    return stars
  }

  return (
    <>
      <figure className="testimonial_ab_fig">
        <img src={testimonialAssets.backgroundImage || "/placeholder.svg"} alt="Testimonial background" />
      </figure>
      <section className="testimonial_section">
        <div className="testimonial_heading">
          <small>
            <strong></strong> <span></span>
            <span></span>
            <span></span>
          </small>
          <h3>{testimonialAssets.heading}</h3>
          <small>
            {" "}
            <span></span>
            <span></span>
            <span></span>
            <strong></strong>
          </small>
        </div>
        <div className="custom_container">
          <div className="testimonial_main">
            <div className="testimonial_list">
              {mounted && (
                <Slider
                  dots={false}
                  infinite={true}
                  speed={2000}
                  slidesToShow={3}
                  slidesToScroll={1}
                  autoplay={true}
                  autoplaySpeed={5000}
                  className="testimonial_slider"
                  adaptiveHeight={true}
                  pauseOnHover={true}
                  swipe={true}
                  responsive={[
                    {
                      breakpoint: 1024, // Tablet breakpoint
                      settings: {
                        slidesToShow: 2,
                        slidesToScroll: 1,
                        infinite: true,
                        dots: false,
                      },
                    },
                    {
                      breakpoint: 640, // Mobile breakpoint
                      settings: {
                        slidesToShow: 1,
                        slidesToScroll: 1,
                      },
                    },
                  ]}
                >
                  {testimonials.map((testimonial: Testimonial) => (
                    <li key={testimonial.id}>
                      <div className="testimonial_data_box">
                        <div className="testimonial_review_data_main">
                          <div className="testimonial_description">
                            <p style={{ height: "152px", overflow: "auto" }}>{testimonial.text}</p>
                          </div>
                          <div className="testimonial_figure_box">
                            <figure>
                              <img
                                className="lazy"
                                src={testimonial.image || "/placeholder.svg"}
                                alt={`${testimonial.name} - Customer`}
                              />
                            </figure>
                          </div>
                        </div>
                        <div className="testimonial_review_data_main-second">
                          <div className="testimonial_rating_stars">{renderStars(testimonial.rating)}</div>
                          <div className="testimonial_reviewer_data">
                            <h4>{testimonial.name}</h4>
                            <span>
                              {testimonial.role}, {testimonial.location}
                            </span>
                          </div>
                        </div>
                      </div>
                    </li>
                  ))}
                </Slider>
              )}
            </div>
          </div>
        </div>
      </section>
    </>
  )
}
