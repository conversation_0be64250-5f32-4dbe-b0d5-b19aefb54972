name: ezeats
services:
  - name: web
    git:
      branch: main
      repo_clone_url: https://github.com/tdusman/Ezeats.git
    build_command: npm install -g pnpm@8 && pnpm install --frozen-lockfile && pnpm build
    run_command: pnpm start
    environment_slug: node-js
    instance_size_slug: basic-xxs
    instance_count: 1
    envs:
      - key: NODE_ENV
        value: production
      - key: GOOGLE_MAPS_API_KEY
        value: ${GOOGLE_MAPS_API_KEY}
      - key: BUSINESS_ID
        value: ${BUSINESS_ID}
      - key: BRANCH_ID
        value: ${BRANCH_ID}
    http_port: 3000
    health_check:
      http_path: /
    cors:
      allow_origins:
        - https://*.tossdown.com
