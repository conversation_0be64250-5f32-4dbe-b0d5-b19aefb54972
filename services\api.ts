import type { ProductsResponse, Product } from "@/types/product";

export async function fetchProducts(): Promise<ProductsResponse> {
  try {
    const response = await fetch(
      `https://tossdown.com/api/products?display_source=2&business_id=12536&branch_id=18784&attributes=1`,
      {
        headers: {
          "Content-Type": "application/json",
        },
        cache: "no-store", // Disable caching to always get fresh data
      }
    );

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error("Error fetching products");
    throw error;
  }
}

// Add this function to fetch a single product by ID
export async function fetchProductById(
  productId: string
): Promise<Product | null> {
  try {
    const data = await fetchProducts();
    if (!data || !data.items) {
      console.error("Invalid data structure returned from fetchProducts");
      return null;
    }
    const product = data.items.find((item) => item.menu_item_id === productId);
    return product || null;
  } catch (error) {
    console.error(`Error fetching product with ID ${productId}:`, error);
    return null;
  }
}

// Add this function to filter valid products
export function filterValidProducts(products: Product[]): Product[] {
  return products.filter(
    (product) =>
      product.display_source === "0" || product.display_source === "2"
  );
}

// Add a new function to filter featured products
export function filterFeaturedProducts(products: Product[]): Product[] {
  return products.filter(
    (product) =>
      // Keep the existing filter conditions
      (product.display_source === "0" || product.display_source === "2") &&
      // Add the new featured filter condition
      product.featured === "1"
  );
}

// Update the mapProductToMenuItem function to use the new isProductVegetarian logic
export function mapProductToMenuItem(product: Product) {
  // Helper function to check if product is vegetarian
  const isVeg = () => {
    if (!product.attributes || !Array.isArray(product.attributes)) return false;

    if (product.attributes.length > 0) {
      const firstAttribute = product.attributes[0];
      return (
        Object.keys(firstAttribute).includes("Veg") &&
        firstAttribute["Veg"] === "Veg"
      );
    }

    return false;
  };

  return {
    id: Number.parseInt(product.menu_item_id) || 0,
    name: product.name || "",
    image: product.image || product.large_image || "",
    rating: Number.parseFloat(product.product_rating) || 0,
    price: Number.parseFloat(product.price) || 0,
    isVeg: isVeg(),
    inStock: product.status !== "1",
    description: product.desc || "",
    sku: product.sku || "",
    currency: product.currency || "",
    reviews: product.reviews || [], // Return empty array instead of fallback review
  };
}
