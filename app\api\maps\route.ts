import { NextResponse } from "next/server";

export async function GET() {
  const apiKey = "AIzaSyC_xNZFI6U-kYWPG7uGY_BSBwMudSj_xgk";

  // Don't return the actual API key, instead return a URL with a signature or token
  // For Google Maps, we can use a URL that already has the API key embedded
  return NextResponse.json({
    // Return only what's needed for initialization, not the raw key
    hasKey: !!apiKey,
    // Return a boolean indicating if the key exists, not the key itself
    status: apiKey ? "available" : "unavailable",
  });
}
