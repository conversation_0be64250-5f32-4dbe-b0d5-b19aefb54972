import { NextResponse } from "next/server"
import { fetchProducts, filterFeaturedProducts } from "@/services/api"

export async function GET() {
  try {
    const data = await fetchProducts()

    // Filter featured products (products with featured = "1")
    const featuredProducts = filterFeaturedProducts(data.items)

    // Return the filtered products data
    return NextResponse.json({
      ...data,
      items: featuredProducts,
    })
  } catch (error) {
    console.error("Error fetching featured products:", error)
    return NextResponse.json({ error: "Failed to fetch featured products" }, { status: 500 })
  }
}
