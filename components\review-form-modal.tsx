"use client";
import { X, Loader2 } from "lucide-react";
import type React from "react";
import { useState, useEffect } from "react";
import { useAuth } from "@/context/auth-context";
import { submitProductReview } from "@/services/review-api";
import { useToast } from "@/context/toast-context";
import { useRouter } from "next/navigation";

interface ReviewFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  productId: string | number;
  productName: string;
  onSubmitReview: (review: {
    rating: number;
    name: string;
    comment: string;
    productId: string | number;
  }) => Promise<void>;
}

const ReviewFormModal = ({
  isOpen,
  onClose,
  productId,
  productName,
  onSubmitReview,
}: ReviewFormModalProps) => {
  const [rating, setRating] = useState(0);
  const [hoveredRating, setHoveredRating] = useState(0);
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [comment, setComment] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuth();
  const { showToast } = useToast();
  const router = useRouter(); // Add this line to get the router

  // Pre-fill name and email if user is logged in
  useEffect(() => {
    if (user) {
      setName(user.name || "");
      setEmail(user.email || "");
    }
  }, [user]);

  if (!isOpen) return null;

  // Update the handleSubmit function to use a business email
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (rating === 0) {
      setError("Please select a rating");
      return;
    }

    if (!name.trim()) {
      setError("Please enter your name");
      return;
    }

    if (!comment.trim()) {
      setError("Please share your experience");
      return;
    }

    if (!productId) {
      setError("Product ID is missing");
      console.error("Product ID is missing:", productId);
      return;
    }

    setError(null);
    setIsSubmitting(true);

    try {
      // Convert product ID to string to ensure API compatibility
      const productIdString = productId.toString();

      // Use business email instead of user email
      const businessEmail = "<EMAIL>";

      // Submit the review using our API service
      const result = await submitProductReview({
        productId: productIdString,
        name,
        email: businessEmail, // Use business email instead of user email
        rating,
        comment,
        title: productName, // Using product name as title
        userId: user?.id,
      });

      if (result.success) {
        // Show success message with the new text
        showToast({
          message: "Thank you for your valuable feedback!",
          type: "success",
          duration: 3000,
        });

        // Also call the parent component's onSubmitReview for local state update
        await onSubmitReview({
          rating,
          name,
          comment,
          productId,
        });

        // Reset form
        setRating(0);
        setName("");
        setEmail("");
        setComment("");

        // Close the review modal
        onClose();

        // Replace the window.location.href line with router.push
        router.push("/");
      } else {
        console.error("Review submission failed:", result);
        setError(
          result.message || "Failed to submit review. Please try again."
        );
      }
    } catch (err) {
      console.error("Error submitting review:", err);
      setError("Failed to submit review. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div
      className="fixed inset-0 bg-black bg-opacity-50 z-[60] flex items-center justify-center p-4"
      onClick={(e) => {
        e.stopPropagation();
        onClose();
      }}
    >
      <div
        className="bg-white rounded-lg w-full max-w-lg p-6 relative"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex justify-end ">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onClose();
            }}
            className="text-black bg-[#f5f5f5] flex justify-center items-center w-[40px] h-[40px] p-0 text-lg absolute top-[-9px] right-[-9px] rounded-full absolute top-0 right-0"
            style={{
              border: "unset",
              outline: "unset",
              boxShadow: "unset",
            }}
            aria-label="Close"
          >
            <X size={24} />
          </button>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <h2 className="text-2xl font-normal mb-4">
              HOW WOULD YOU RATE THIS PRODUCT?
            </h2>
            <div className="flex gap-2">
              {[1, 2, 3, 4, 5].map((star) => (
                <button
                  key={star}
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    setRating(star);
                  }}
                  onMouseEnter={() => setHoveredRating(star)}
                  onMouseLeave={() => setHoveredRating(0)}
                  className="text-4xl focus:outline-none"
                  aria-label={`Rate ${star} stars`}
                >
                  <span className="sr-only">Rate {star} stars</span>
                  {star <= (hoveredRating || rating) ? (
                    <span className="text-yellow-400">★</span>
                  ) : (
                    <span className="text-gray-300">☆</span>
                  )}
                </button>
              ))}
            </div>
          </div>

          <div className="border-t pt-6">
            <label htmlFor="fullName" className="block text-lg font-bold mb-2">
              FULL NAME
            </label>
            <input
              type="text"
              id="fullName"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Enter your full name"
              className="w-full border border-gray-300 rounded p-3 focus:outline-none focus:ring-2 focus:ring-black"
            />
          </div>

          <div>
            <label
              htmlFor="experience"
              className="block text-lg font-bold mb-2"
            >
              TELL US YOUR EXPERIENCE
            </label>
            <textarea
              id="experience"
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              placeholder="Write your reviews"
              rows={5}
              className="w-full border border-gray-300 rounded p-3 focus:outline-none focus:ring-2 focus:ring-black"
            />
          </div>

          {error && <div className="text-red-500 text-sm">{error}</div>}

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={isSubmitting}
              className="bg-black text-white px-6 py-3 rounded-full font-medium disabled:opacity-70"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="inline-block mr-2 h-4 w-4 animate-spin" />
                  SUBMITTING...
                </>
              ) : (
                "SUBMIT REVIEW"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ReviewFormModal;
