import { Skeleton } from "@/components/ui/skeleton"

export function BusinessSkeleton() {
  return (
    <div className="w-full">
      {/* Header skeleton */}
      <div className="bg-white shadow-sm">
        <div className="container mx-auto px-4 max-w-7xl flex items-center py-4">
          <div className="flex items-center flex-1">
            <Skeleton className="h-[80px] w-[200px]" />
            <div className="hidden md:flex items-center space-x-6 ml-8">
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-12" />
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-24" />
            </div>
          </div>
          <div className="flex items-center space-x-4">
            <Skeleton className="h-8 w-20 rounded-full" />
            <Skeleton className="h-8 w-20 rounded-full" />
            <Skeleton className="h-6 w-6 rounded-full" />
          </div>
        </div>
      </div>

      {/* Full-width hero section skeleton */}
      <div className="w-full h-[400px] md:h-[500px] lg:h-[600px] bg-gray-200 animate-pulse">
        <div className="container mx-auto px-4 pt-8">
          <Skeleton className="h-10 w-48 mb-6" />
          <Skeleton className="h-12 w-64 mb-4" />
          <div className="max-w-md space-y-4">
            <Skeleton className="h-12 w-full rounded-md" />
            <div className="flex gap-2">
              <Skeleton className="h-12 flex-1 rounded-md" />
              <Skeleton className="h-12 w-24 rounded-md" />
            </div>
          </div>
        </div>
      </div>

      {/* Main content skeleton */}
      <div className="container mx-auto px-4 max-w-7xl py-8">
        <div className="flex flex-col space-y-4">
          <Skeleton className="h-8 w-3/4 mx-auto" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex flex-col items-center space-y-4">
                <Skeleton className="h-16 w-16 rounded-full" />
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-48" />
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Branch selector skeleton */}
      <div className="container mx-auto px-4 max-w-md my-8">
        <Skeleton className="h-12 w-full rounded-md" />
      </div>
    </div>
  )
}
