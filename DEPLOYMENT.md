# Deployment Guide for EZeats on Digital Ocean

This guide provides instructions for deploying the EZeats application to Digital Ocean App Platform, with a focus on ensuring that the Google Maps integration works correctly.

## Prerequisites

1. A Digital Ocean account
2. A valid Google Maps API key with the Places API enabled
3. Your codebase ready for deployment

## Setting Up Environment Variables

The most common issue with Google Maps not working in production is related to environment variables. Follow these steps to properly configure them:

### 1. In the Digital Ocean App Platform Dashboard

1. Navigate to your app in the Digital Ocean App Platform
2. Go to the "Settings" tab
3. Scroll down to the "Environment Variables" section
4. Add the following environment variables:

   - `GOOGLE_MAPS_API_KEY`: Your Google Maps API key
   - `BUSINESS_ID`: 12536 (or your specific business ID)
   - `BRANCH_ID`: 18784 (or your specific branch ID)

5. Make sure to click "Save" after adding the variables

### 2. Verify API Key Restrictions

If your Google Maps API key has domain restrictions, make sure to:

1. Add your Digital Ocean app domain to the allowed domains in the Google Cloud Console
2. Include both `https://your-app-domain.com` and `https://www.your-app-domain.com`

## Troubleshooting Google Maps Issues

If you're still experiencing issues with Google Maps after deployment, try the following:

### Check Console Logs

1. Open your deployed application
2. Open the browser developer tools (F12 or right-click > Inspect)
3. Go to the Console tab
4. Look for any errors related to Google Maps

Common errors include:
- "Google Maps API key is missing"
- "Google has disabled use of the Maps JavaScript API"
- "RefererNotAllowedMapError"

### Verify Environment Variables

1. In your Digital Ocean app, go to the "Logs" tab
2. Look for logs from the server startup
3. Check if there are any messages about missing environment variables

### Test API Key Directly

1. Create a simple HTML file with the following content:
```html
<!DOCTYPE html>
<html>
<head>
  <title>Google Maps API Test</title>
</head>
<body>
  <div id="map" style="height: 400px; width: 100%;"></div>
  <script>
    function initMap() {
      new google.maps.Map(document.getElementById('map'), {
        center: {lat: -34.397, lng: 150.644},
        zoom: 8
      });
    }
  </script>
  <script src="https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&callback=initMap" async defer></script>
</body>
</html>
```
2. Replace `YOUR_API_KEY` with your actual API key
3. Open this file in a browser to see if the map loads

## Deployment Checklist

Before deploying, make sure to:

1. ✅ Set up all required environment variables in Digital Ocean
2. ✅ Configure Google Maps API key with proper domain restrictions
3. ✅ Build your application with production settings
4. ✅ Test the application locally with production settings

## Additional Resources

- [Digital Ocean App Platform Documentation](https://docs.digitalocean.com/products/app-platform/)
- [Google Maps JavaScript API Documentation](https://developers.google.com/maps/documentation/javascript/overview)
- [Next.js Deployment Documentation](https://nextjs.org/docs/deployment)

## Support

If you continue to experience issues, please:

1. Check the application logs in Digital Ocean
2. Verify that your Google Maps API key is valid and has the correct APIs enabled
3. Ensure that your API key is properly restricted to your domain
4. Contact Digital Ocean support if you believe there's an issue with the platform configuration
