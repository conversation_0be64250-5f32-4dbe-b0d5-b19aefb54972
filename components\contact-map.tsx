"use client"

import { useEffect, useRef } from "react"
import { useGoogleMaps } from "@/hooks/use-google-maps"

export default function ContactMap() {
  const mapRef = useRef<HTMLDivElement>(null)
  const { isLoaded, error } = useGoogleMaps()

  useEffect(() => {
    if (isLoaded && mapRef.current && window.google?.maps) {
      // Example coordinates for Toronto
      const location = { lat: 43.6532, lng: -79.3832 }

      const map = new window.google.maps.Map(mapRef.current, {
        center: location,
        zoom: 15,
        mapTypeControl: false,
        fullscreenControl: false,
        streetViewControl: false,
      })

      // Add a marker for the location
      new window.google.maps.Marker({
        position: location,
        map,
        title: "EZeats",
      })
    }
  }, [isLoaded])

  if (error) {
    return (
      <div className="bg-gray-100 h-[300px] flex items-center justify-center">
        <p className="text-gray-500">Map could not be loaded</p>
      </div>
    )
  }

  return (
    <div ref={mapRef} className="h-[300px] w-full rounded-lg overflow-hidden" aria-label="Map showing our location">
      {!isLoaded && (
        <div className="bg-gray-100 h-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
        </div>
      )}
    </div>
  )
}
