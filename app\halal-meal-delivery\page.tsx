import type { Metadata } from "next";
import HalalMealsClient from "./client";

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: "Fresh Halal Meal Delivery in Mississauga | EZeats",
    description:
      "Enjoy the most affordable halal meal delivery service that provides fresh, halal-certified meals and tiffin service for delivery in just an hour!",
    keywords:
      "halal meal delivery, halal meals, halal food, meal delivery service, tiffin service, indian food, meal kit, halal certified, halal meat, homemade meals, Mississauga, Etobicoke, Brampton",
  };
}

export default async function HalalMealsPage() {
  // Try to get the H1 from XML, or use a default
  const h1 = "Delicious Halal Meal Delivery Service";

  return (
    <>
      {/* Use H1 from XML if available */}
      {h1 && <h1 className="sr-only">{h1}</h1>}
      <HalalMealsClient initialH1={h1} />
    </>
  );
}
