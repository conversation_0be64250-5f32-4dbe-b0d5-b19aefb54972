import { NextResponse } from "next/server";

export async function GET() {
  const apiKey = "AIzaSyC_xNZFI6U-kYWPG7uGY_BSBwMudSj_xgk";

  if (!apiKey) {
    console.warn(
      "Google Maps API key is missing. Using a placeholder for production/development."
    );

    // For production/development when key is missing, load the script without a key
    // This will allow the app to load, but maps functionality won't work

    // Return a JavaScript file that will initialize a dummy Google Maps object
    return new NextResponse(
      `
      console.log("Loading mock Google Maps API due to missing API key");
      // Mock Google Maps API
      window.google = window.google || {};
      window.google.maps = window.google.maps || {};
      window.google.maps.places = window.google.maps.places || {};
      window.google.maps.places.Autocomplete = function() {
        return {
          addListener: function(event, callback) {
            console.warn('Google Maps API key is missing. Maps functionality is limited.');
            // Call the callback immediately to prevent blocking
            if (event === 'place_changed' && typeof callback === 'function') {
              setTimeout(callback, 100);
            }
          },
          getPlace: function() {
            return { formatted_address: '' };
          }
        };
      };

      window.google.maps.Map = function() {
        return {};
      };

      window.google.maps.Marker = function() {
        return {};
      };

      // Call the initialization function
      if (typeof window.initGoogleMapsAutocomplete === 'function') {
        window.initGoogleMapsAutocomplete();
      }
      `,
      {
        headers: {
          "Content-Type": "application/javascript",
        },
      }
    );
  }

  try {
    // If we have an API key, load the Google Maps script directly
    // Instead of redirecting, we'll fetch and return the script content
    // This avoids potential CORS issues in production
    console.log("Loading Google Maps with API key");
    const scriptUrl = `https://maps.googleapis.com/maps/api/js?key=${apiKey}&libraries=places&callback=initGoogleMapsAutocomplete`;

    // Return the script URL for the client to load
    return NextResponse.json({ scriptUrl });
  } catch (error) {
    console.error("Error in Google Maps script route:", error);

    // Return a fallback script in case of error
    return new NextResponse(
      `
      console.error("Error loading Google Maps API, using fallback");
      // Fallback Google Maps API
      window.google = window.google || {};
      window.google.maps = window.google.maps || {};
      window.google.maps.places = window.google.maps.places || {};
      window.google.maps.places.Autocomplete = function() {
        return {
          addListener: function(event, callback) {
            console.warn('Error loading Google Maps API. Using fallback implementation.');
            if (event === 'place_changed' && typeof callback === 'function') {
              setTimeout(callback, 100);
            }
          },
          getPlace: function() {
            return { formatted_address: '' };
          }
        };
      };

      // Call the initialization function
      if (typeof window.initGoogleMapsAutocomplete === 'function') {
        window.initGoogleMapsAutocomplete();
      }
      `,
      {
        headers: {
          "Content-Type": "application/javascript",
        },
      }
    );
  }
}
