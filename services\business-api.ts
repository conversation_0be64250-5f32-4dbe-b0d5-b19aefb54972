import type {
  BusinessLocationsResponse,
  Branch,
  BranchSettings,
  BranchTiming,
} from "@/types/business";
import { BUSINESS_ID, BRANCH_ID } from "@/constants/app-constants";

const BUSINESS_LOCATIONS_API = `https://d9gwfwdle3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/locations/${BRANCH_ID}`;

// Parse the branch settings from JSON string to object
function parseBranchSettings(branch: Branch): Branch {
  try {
    if (branch.settings) {
      const parsedSettings = JSON.parse(branch.settings) as BranchSettings;
      return {
        ...branch,
        parsedSettings,
      };
    }
  } catch (error) {
    console.error("Error parsing branch settings");
  }
  return branch;
}

// Timing validation - check if business is open right now
// Supports multiple timing slots per day (e.g., lunch breaks)
// Uses business timezone offset for accurate validation
function validateBusinessHours(branch: Branch): boolean {
  try {
    if (!branch.timing) {
      return true;
    }

    const timingData = JSON.parse(branch.timing);

    // Get current time with business timezone offset applied
    const businessTime = getCurrentBusinessTime(branch.time_zone);
    const currentDay = businessTime.dayNumber;
    const currentMinutes = businessTime.minutes;

    const dayHours = timingData.hours?.[currentDay.toString()];
    if (!dayHours || dayHours.length === 0) {
      return false;
    }

    // Check each timing slot for the day
    for (const slot of dayHours) {
      if (!slot.timing || slot.timing.length === 0) {
        return true; // 24/7 operation
      }

      // Check each time range within the slot
      for (const timeRange of slot.timing) {
        if (
          timeRange.length === 2 &&
          typeof timeRange[0] === "number" &&
          typeof timeRange[1] === "number"
        ) {
          const openTime = timeRange[0];
          const closeTime = timeRange[1];

          if (openTime === closeTime) {
            continue; // Closed period
          }

          if (currentMinutes >= openTime && currentMinutes <= closeTime) {
            return true; // Open
          }
        }
      }
    }

    return false;
  } catch (error) {
    return true; // Default to open if error
  }
}

// Parse the branch timing from JSON string to object
function parseBranchTiming(branch: Branch): Branch {
  try {
    if (branch.timing) {
      const parsedTiming = JSON.parse(branch.timing) as BranchTiming;
      return {
        ...branch,
        parsedTiming,
      };
    }
  } catch (error) {
    // Silent error handling
  }
  return branch;
}

export async function fetchBusinessLocations(): Promise<BusinessLocationsResponse> {
  try {
    const response = await fetch(BUSINESS_LOCATIONS_API);

    if (!response.ok) {
      throw new Error(`API error: ${response.status}`);
    }

    const data = (await response.json()) as BusinessLocationsResponse;

    // Parse branch settings and timing for each branch
    if (data.result && data.result.branches) {
      data.result.branches = data.result.branches
        .map(parseBranchSettings)
        .map(parseBranchTiming);
    }

    return data;
  } catch (error) {
    throw error;
  }
}

// Get minimum spend amount from branch settings
export function getMinimumSpend(branch: Branch): number {
  if (branch.parsedSettings?.cart?.minimum_spent) {
    return Number.parseFloat(branch.parsedSettings.cart.minimum_spent);
  }
  return 0;
}

// Get formatted address for a branch
export function getFormattedBranchAddress(branch: Branch): string {
  return `${branch.address}, ${branch.city}, ${branch.location}, ${branch.country}`;
}

// Convert minutes since midnight to 12-hour format time string (e.g., 600 -> "10:00 AM")
function minutesToTimeString12Hour(minutes: number): string {
  const hours24 = Math.floor(minutes / 60);
  const mins = minutes % 60;

  // Convert to 12-hour format
  const hours12 = hours24 === 0 ? 12 : hours24 > 12 ? hours24 - 12 : hours24;
  const ampm = hours24 >= 12 ? "PM" : "AM";

  return `${hours12}:${mins.toString().padStart(2, "0")} ${ampm}`;
}

// Get current date and time with business timezone offset applied
function getCurrentBusinessTime(timeZoneOffset?: string): {
  date: Date;
  dayNumber: number;
  minutes: number;
} {
  const now = new Date();

  if (!timeZoneOffset) {
    return {
      date: now,
      dayNumber: now.getDay() === 0 ? 7 : now.getDay(),
      minutes: now.getHours() * 60 + now.getMinutes(),
    };
  }

  // Parse offset like "-04:00" or "+05:30"
  const offsetMatch = timeZoneOffset.match(/^([+-])(\d{2}):(\d{2})$/);
  if (!offsetMatch) {
    return {
      date: now,
      dayNumber: now.getDay() === 0 ? 7 : now.getDay(),
      minutes: now.getHours() * 60 + now.getMinutes(),
    };
  }

  const [, sign, hours, minutes] = offsetMatch;
  const offsetMinutes =
    (parseInt(hours) * 60 + parseInt(minutes)) * (sign === "+" ? 1 : -1);

  // Convert user time to UTC, then apply business timezone offset
  // Get UTC time by removing user's timezone offset
  const utcTime = new Date(now.getTime() + now.getTimezoneOffset() * 60 * 1000);

  // Apply business timezone offset to UTC time
  const businessTime = new Date(utcTime.getTime() + offsetMinutes * 60 * 1000);

  return {
    date: businessTime,
    dayNumber: businessTime.getDay() === 0 ? 7 : businessTime.getDay(),
    minutes: businessTime.getHours() * 60 + businessTime.getMinutes(),
  };
}

// Check if business is open based on hours
export function isBranchOpen(branch: Branch): boolean {
  return validateBusinessHours(branch);
}

// Get business status with next opening time
export function getBranchStatus(branch: Branch): {
  isOpen: boolean;
  status: "open" | "closed" | "open_24_7";
  nextOpenTime?: string;
  todayHours?: string;
} {
  if (!branch.parsedTiming) {
    return { isOpen: true, status: "open" };
  }

  const businessTime = getCurrentBusinessTime(branch.time_zone);
  const currentDay = businessTime.dayNumber.toString();
  const dayTimings = branch.parsedTiming.hours?.[currentDay];

  if (!dayTimings || dayTimings.length === 0) {
    return { isOpen: false, status: "closed" };
  }

  // Check if any timing slot is 24/7
  for (const timingSlot of dayTimings) {
    if (!timingSlot.timing || timingSlot.timing.length === 0) {
      return { isOpen: true, status: "open_24_7" };
    }
  }

  const isOpen = isBranchOpen(branch);

  // Get today's hours for display - handle multiple timing slots per day
  let todayHours = "";
  const allTimeRanges: number[][] = [];

  // Collect all time ranges from all timing slots for the day
  for (const timingSlot of dayTimings) {
    if (timingSlot.timing && timingSlot.timing.length > 0) {
      allTimeRanges.push(...timingSlot.timing);
    }
  }

  if (allTimeRanges.length > 0) {
    // Filter valid ranges and sort them by start time
    const validRanges = allTimeRanges
      .filter(
        (range) =>
          range.length === 2 &&
          typeof range[0] === "number" &&
          typeof range[1] === "number" &&
          range[0] !== range[1] // Exclude closed periods (same start/end time)
      )
      .sort((a, b) => a[0] - b[0]); // Sort by start time

    const ranges = validRanges.map(
      (range) =>
        `${minutesToTimeString12Hour(
          range[0] as number
        )} - ${minutesToTimeString12Hour(range[1] as number)}`
    );

    todayHours = ranges.join(", ");
  }

  return {
    isOpen,
    status: (isOpen ? "open" : "closed") as "open" | "closed",
    todayHours: todayHours || undefined,
  };
}

// Check if branch allows ordering for future delivery
export function canOrderForLater(branch: Branch): boolean {
  // This function can be extended to check business settings for future orders
  // For now, return true if branch has timing data
  return !!branch.parsedTiming;
}

// Comprehensive business hours validation utility
export function validateBusinessHoursWithMessage(
  branch: Branch | null,
  deliveryTiming: string = "now"
): {
  isValid: boolean;
  message: string;
  status: "open" | "closed" | "open_24_7" | "no_branch";
  todayHours?: string;
} {
  // Check if branch exists
  if (!branch) {
    return {
      isValid: false,
      message: "No branch selected",
      status: "no_branch",
    };
  }

  // For scheduled orders, always allow
  if (
    deliveryTiming === "later" ||
    deliveryTiming === "weekly" ||
    deliveryTiming === "biweekly"
  ) {
    return {
      isValid: true,
      message: "Scheduled order - no time restriction",
      status: "open",
    };
  }

  // For immediate orders, check business hours
  const isOpen = isBranchOpen(branch);
  const status = getBranchStatus(branch);

  if (!isOpen) {
    const hoursInfo = status.todayHours
      ? ` Today's hours: ${status.todayHours}`
      : "";
    return {
      isValid: false,
      message: `Business is currently closed.${hoursInfo} Please schedule your order for later.`,
      status: status.status,
      todayHours: status.todayHours,
    };
  }

  return {
    isValid: true,
    message: status.status === "open_24_7" ? "Open 24/7" : "Business is open",
    status: status.status,
    todayHours: status.todayHours,
  };
}
