import { Skeleton } from "@/components/ui/skeleton"

export default function MenuPageSkeleton() {
  return (
    <main className="min-h-screen py-16">
      <div className="container mx-auto px-4 max-w-7xl">
        <Skeleton className="h-12 w-64 mx-auto mb-12" />

        <div className="grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-4 gap-x-4 gap-y-10 md:gap-x-6">
          {[...Array(12)].map((_, index) => (
            <div key={index} className="flex flex-col items-center">
              <Skeleton className="w-full aspect-square mb-3" />
              <Skeleton className="h-8 w-3/4 mb-3" />
              <div className="flex justify-center mb-2">
                {[...Array(5)].map((_, i) => (
                  <Skeleton key={i} className="w-5 h-5 rounded-full mx-0.5" />
                ))}
              </div>
              <Skeleton className="h-6 w-1/2" />
            </div>
          ))}
        </div>
      </div>
    </main>
  )
}
