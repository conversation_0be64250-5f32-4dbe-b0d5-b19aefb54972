"use client";

import { useState, useEffect } from "react";
import { useBusiness } from "@/context/business-context";

// Global state to track if the script is loading or has loaded
let isLoading = false;
let isLoaded = false;
let loadError: string | null = null;
let callbacks: Array<() => void> = [];

export function useGoogleMaps() {
  const [scriptLoaded, setScriptLoaded] = useState(isLoaded);
  const [error, setError] = useState<string | null>(loadError);
  const { googleMapsApiKey } = useBusiness();

  useEffect(() => {
    // If already loaded, just update state
    if (isLoaded) {
      setScriptLoaded(true);
      return;
    }

    // If already loading, add callback to be called when loaded
    if (isLoading) {
      callbacks.push(() => setScriptLoaded(true));
      return;
    }

    // Define the initialization function that Google will call
    if (!window.initGoogleMapsAutocomplete) {
      window.initGoogleMapsAutocomplete = () => {
        console.log("Google Maps initialization callback triggered");
        isLoaded = true;
        isLoading = false;
        setScriptLoaded(true);

        // Call all registered callbacks
        callbacks.forEach((callback) => callback());
        callbacks = [];
      };
    }

    // Start loading the script
    isLoading = true;

    // Instead of using the API key directly, load the script via a server action or API route
    const loadGoogleMapsScript = async () => {
      try {
        console.log("Starting Google Maps script loading process");

        // First check if the API is available
        const apiCheckResponse = await fetch("/api/maps");
        const apiStatus = await apiCheckResponse.json();

        if (!apiStatus.hasKey) {
          console.warn(
            "Google Maps API key is not available. Maps functionality will be limited."
          );
        }

        // Get the script URL or content from our API endpoint
        const scriptResponse = await fetch("/api/maps/script");

        // Check if we got a JSON response with a script URL
        let scriptUrl = "";
        let isJsonResponse = false;

        try {
          const contentType = scriptResponse.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            isJsonResponse = true;
            const data = await scriptResponse.json();
            if (data.scriptUrl) {
              scriptUrl = data.scriptUrl;
              console.log("Received script URL from API:", scriptUrl);
            }
          }
        } catch (e) {
          console.log("Response is not JSON, assuming it's a script");
        }

        if (isJsonResponse && scriptUrl) {
          // We got a script URL, load it directly
          const googleMapScript = document.createElement("script");
          googleMapScript.src = scriptUrl;
          googleMapScript.async = true;
          googleMapScript.defer = true;
          googleMapScript.id = "google-maps-script";

          // Handle script load success
          googleMapScript.onload = () => {
            console.log("Google Maps script loaded successfully via URL");
          };

          // Handle script load errors
          googleMapScript.onerror = (e) => {
            console.error("Error loading Google Maps script from URL:", e);
            createFallbackImplementation();
          };

          // Check if script already exists
          if (!document.getElementById("google-maps-script")) {
            document.head.appendChild(googleMapScript);
          }
        } else {
          // We got a script content directly, evaluate it
          const scriptContent = await scriptResponse.text();
          console.log(
            "Received script content from API, length:",
            scriptContent.length
          );

          // Create a script element and set its content
          const googleMapScript = document.createElement("script");
          googleMapScript.id = "google-maps-script";
          googleMapScript.textContent = scriptContent;

          // Check if script already exists
          if (!document.getElementById("google-maps-script")) {
            document.head.appendChild(googleMapScript);
          }
        }

        // Function to create fallback implementation
        function createFallbackImplementation() {
          isLoading = false;
          loadError = "Failed to load Google Maps API";
          setError(loadError);

          // Create a fallback implementation
          if (!window.google) {
            window.google = {
              maps: {
                places: {
                  Autocomplete: function () {
                    return {
                      addListener: function (
                        event: string,
                        callback: () => void
                      ) {
                        console.warn(
                          "Google Maps API failed to load. Using fallback implementation."
                        );
                        // Call the callback immediately to prevent blocking
                        if (
                          event === "place_changed" &&
                          typeof callback === "function"
                        ) {
                          setTimeout(callback, 100);
                        }
                      },
                      getPlace: function () {
                        return { formatted_address: "" };
                      },
                    };
                  },
                },
                Map: function () {
                  return {};
                },
                Marker: function () {
                  return {};
                },
              },
            };
          }

          // Call the initialization function to unblock the UI
          if (typeof window.initGoogleMapsAutocomplete === "function") {
            window.initGoogleMapsAutocomplete();
          }

          // Call all registered callbacks with error
          callbacks.forEach((callback) => callback());
          callbacks = [];
        }
      } catch (error) {
        console.error("Error initializing Google Maps:", error);
        isLoading = false;
        loadError = "Failed to initialize Google Maps";
        setError(loadError);

        // Create fallback implementation
        if (!window.google) {
          window.google = {
            maps: {
              places: {
                Autocomplete: function () {
                  return {
                    addListener: function (
                      event: string,
                      callback: () => void
                    ) {
                      console.warn(
                        "Google Maps API failed to load. Using fallback implementation."
                      );
                      // Call the callback immediately to prevent blocking
                      if (
                        event === "place_changed" &&
                        typeof callback === "function"
                      ) {
                        setTimeout(callback, 100);
                      }
                    },
                    getPlace: function () {
                      return { formatted_address: "" };
                    },
                  };
                },
              },
              Map: function () {
                return {};
              },
              Marker: function () {
                return {};
              },
            },
          };
        }

        // Call the initialization function to unblock the UI
        if (typeof window.initGoogleMapsAutocomplete === "function") {
          window.initGoogleMapsAutocomplete();
        }
      }
    };

    loadGoogleMapsScript();

    return () => {
      // Don't remove the script on component unmount
      // Just remove this component's callback
      callbacks = callbacks.filter(
        (callback) => callback !== (() => setScriptLoaded(true))
      );
    };
  }, [googleMapsApiKey]);

  return { isLoaded: scriptLoaded, error };
}

// Add type definition for the global window object
declare global {
  interface Window {
    google: any;
    initGoogleMapsAutocomplete: () => void;
  }
}
