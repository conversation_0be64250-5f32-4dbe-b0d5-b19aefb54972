import { v4 as uuidv4 } from "uuid"
import type { CartItem } from "@/context/cart-context"
import { CART_API_URL, BUSINESS_ID, BRANCH_ID } from "@/constants/app-constants"

// Get or create a unique order ID
export function getUniqueOrderId(): string {
  // Check if we already have a unique order ID in localStorage
  let uniqueOrderId = localStorage.getItem("unique_order_id")

  // If not, create a new one and store it
  if (!uniqueOrderId) {
    uniqueOrderId = uuidv4()
    localStorage.setItem("unique_order_id", uniqueOrderId)
  }

  return uniqueOrderId
}

// Format cart items for the API
// Always send quantity as 1 for all actions
function formatCartItems(items: CartItem[]) {
  return items.map((item) => ({
    id: item.id.toString(),
    image: item.image,
    name: item.name,
    price: item.price.toString(),
    qty: 1, // Always set quantity to 1 regardless of actual quantity
    discount: 0,
    item_level_discount_value: 0,
    tax: "0",
    item_level_tax_value: 0,
    weight_value: "0",
    weight_unit: "kg",
    comment: "",
    category_id: "0", // We don't have this info in our current cart item structure
    product_code: "0",
    category_name: item.isVeg ? "Veg" : "Non-Veg", // Using isVeg as a simple category
    brand_id: "0",
    options: {},
  }))
}

// Cart API actions
type CartAction = "add" | "sub" | "delete"

// Update cart on the server
export async function updateCart(action: CartAction, items: CartItem[], orderType: "delivery" | "pickup" = "delivery") {
  try {
    const uniqueOrderId = getUniqueOrderId()
    const currentDate = new Date().toISOString()

    const payload = {
      action,
      current_date: currentDate,
      unique_order_id: uniqueOrderId,
      order_type: orderType,
      business_id: BUSINESS_ID,
      branch_id: BRANCH_ID,
      items: formatCartItems(items),
    }

    const response = await fetch(CART_API_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(payload),
    })

    // Try to parse the response as JSON
    let data
    try {
      data = await response.json()
    } catch (jsonError) {
      console.error("Error parsing API response as JSON:", jsonError)
      // If we can't parse as JSON, create a basic response object
      data = {
        status: response.ok ? "success" : "error",
        message: response.ok ? "Operation completed" : `HTTP error: ${response.status}`,
        ok: response.ok,
      }
    }

    // If the response is not ok, add more context to the data
    if (!response.ok) {
      data.httpStatus = response.status
      data.httpStatusText = response.statusText
    }

    return data
  } catch (error) {
    console.error("Error updating cart:", error)
    // Return a basic error response
    return {
      status: "error",
      message: error instanceof Error ? error.message : "Unknown error occurred",
      error: error,
    }
  }
}

// Add item to cart
export async function addToCart(item: CartItem, orderType: "delivery" | "pickup" = "delivery") {
  return updateCart("add", [item], orderType)
}

// Remove item from cart
export async function removeFromCart(item: CartItem, orderType: "delivery" | "pickup" = "delivery") {
  return updateCart("sub", [item], orderType)
}

// Delete item from cart
export async function deleteFromCart(item: CartItem, orderType: "delivery" | "pickup" = "delivery") {
  return updateCart("delete", [item], orderType)
}
