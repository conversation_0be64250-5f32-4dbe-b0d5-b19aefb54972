"use client";

import type React from "react";

import Image from "next/image";
import { useCart } from "@/context/cart-context";
import { useToast } from "@/context/toast-context";
import { Plus, Minus, Loader2 } from "lucide-react";
import { useRef, useEffect, useState } from "react";
import { useProductModal } from "@/context/product-modal-context";
import Link from "next/link";
import { usePathname } from "next/navigation";
// If there are direct links to product pages, update them to use the new slug format
import { generateProductSlug } from "@/utils/product-utils";
import StarRating from "./star-rating";

// Update the props type to include hideAddToCart
type MenuItemCardProps = {
  id: number;
  name: string;
  image: string;
  price: number;
  isVeg: boolean;
  rating: number;
  inStock?: boolean;
  hideAddToCart?: boolean;
};

// Update the function parameters to include hideAddToCart with a default value of false
export default function MenuItemCard({
  id,
  name,
  image,
  price,
  isVeg,
  rating,
  inStock = true,
  hideAddToCart = false,
}: MenuItemCardProps) {
  const {
    addItem,
    items,
    updateQuantity,
    removeItem,
    isBoxFull,
    boxSize,
    isLoading,
    loadingItemId,
    loadingOperation,
  } = useCart();
  const { showToast } = useToast();
  const isAddingRef = useRef(false);
  const { openProductModal } = useProductModal();
  const pathname = usePathname();
  const [isOurMenuPage, setIsOurMenuPage] = useState(false);

  // Determine if we're on the our-menu page
  useEffect(() => {
    setIsOurMenuPage(
      pathname === "/our-menu" || pathname.startsWith("/our-menu/")
    );
  }, [pathname]);

  const cartItem = items.find((item) => item.id === id);

  // Check if this specific item is loading and which operation is being performed
  const isItemLoading = isLoading && loadingItemId === id;
  const isAddLoading = isItemLoading && loadingOperation === "add";
  const isRemoveLoading = isItemLoading && loadingOperation === "remove";

  const handleIncrement = () => {
    // Prevent duplicate clicks or if already loading
    if (isAddingRef.current || isItemLoading) return;
    isAddingRef.current = true;

    // If item is out of stock, show toast and return
    if (!inStock) {
      showToast({
        message: `${name} is currently out of stock`,
        type: "warning",
        duration: 4000,
      });
      isAddingRef.current = false;
      return;
    }

    // If box is full, show toast instead of adding item
    if (isBoxFull && !cartItem) {
      // Different messages based on box size
      const message =
        boxSize === 6
          ? "To add more meals, please update your cart to 12 meals"
          : "You have reached your maximum meal limit for your box size, please proceed to checkout.";

      showToast({
        message,
        type: "error",
        duration: 4000,
      });
      isAddingRef.current = false;
      return;
    }

    // If adding more of an existing item would exceed the limit
    if (cartItem) {
      const totalItemsExcludingThis = items.reduce((total, item) => {
        return item.id === id ? total : total + item.quantity;
      }, 0);

      if (
        boxSize &&
        totalItemsExcludingThis + cartItem.quantity + 1 > boxSize
      ) {
        // Different messages based on box size
        const message =
          boxSize === 6
            ? "To add more meals, please update your cart to 12 meals"
            : "You have reached your maximum meal limit for your box size, please proceed to checkout.";

        showToast({
          message,
          type: "error",
          duration: 4000,
        });
        isAddingRef.current = false;
        return;
      }

      updateQuantity(id, cartItem.quantity + 1);
    } else {
      addItem({ id, name, image, price, isVeg });
    }

    // Reset after a short delay
    setTimeout(() => {
      isAddingRef.current = false;
    }, 300);
  };

  const handleDecrement = () => {
    if (cartItem && cartItem.quantity > 0 && !isItemLoading) {
      updateQuantity(id, cartItem.quantity - 1);
    }
  };

  // Update the handleImageClick function to properly handle the click event
  const handleImageClick = (e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent event bubbling

    // If we're on the our-menu page, navigate to product detail page
    // Otherwise, open the modal
    if (isOurMenuPage) {
      // Navigation will be handled by the Link component
    } else {
      openProductModal(id);
    }
  };

  // Replace the conditional rendering at the end of the component with this:
  return (
    <div className="flex flex-col items-center w-full single_card_box">
      {/* 1. Image - Perfectly square with consistent dimensions */}
      <div className="w-full aspect-square relative mb-3 overflow-hidden rounded-md cursor-pointer">
        {isOurMenuPage ? (
          <Link
            href={`/product/${generateProductSlug(name, id)}`}
            className="block w-full h-full"
          >
            <Image
              src={image || "/placeholder.svg"}
              alt={name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 280px"
            />
          </Link>
        ) : (
          <div onClick={handleImageClick} className="w-full h-full">
            <Image
              src={image || "/placeholder.svg"}
              alt={name}
              fill
              className="object-cover"
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 280px"
            />
          </div>
        )}
        <div className="absolute top-2 left-2 bg-black text-white text-xs px-2 py-1 rounded-sm">
          {isVeg ? "Veg" : "Non-Veg"}
        </div>
      </div>

      {/* 2. Quantity selector - Full width of card */}
      {inStock && !hideAddToCart && (
        <div className="w-full border border-gray-300 rounded-full flex items-center justify-between px-4 py-1 mb-3">
          <button
            onClick={handleDecrement}
            className="p-2 text-black w-8 h-8 flex items-center justify-center"
            disabled={!cartItem || cartItem.quantity === 0 || isItemLoading}
          >
            {isRemoveLoading ? (
              <Loader2 size={18} className="animate-spin" />
            ) : (
              <Minus
                size={18}
                className={isItemLoading ? "text-gray-400" : ""}
              />
            )}
          </button>
          <span className="text-lg font-medium">
            {cartItem ? cartItem.quantity : 0}
          </span>
          <button
            onClick={handleIncrement}
            className="p-2 text-black w-8 h-8 flex items-center justify-center"
            disabled={!inStock || isItemLoading}
          >
            {isAddLoading ? (
              <Loader2 size={18} className="animate-spin" />
            ) : (
              <Plus
                size={18}
                className={isItemLoading ? "text-gray-400" : ""}
              />
            )}
          </button>
        </div>
      )}

      {/* OUT OF STOCK indicator */}
      {!inStock && !hideAddToCart && (
        <div className="text-red-600 font-medium text-sm mb-3 h-[42px] flex items-center">
          OUT OF STOCK
        </div>
      )}

      {/* 3. Name - Below quantity selector */}
      <div className="pro_name text-center font-bold text-lg mb-2">{name}</div>

      {/* 4. Star ratings */}
      <div className="flex justify-center mb-2 product_rating_parent">
        <StarRating rating={rating} starSize={20} fillColor="#f97316" />
      </div>

      {/* 5. Price - At the bottom */}
      <p className="text-center font-medium">CAD {price.toFixed(2)}</p>
    </div>
  );
}
