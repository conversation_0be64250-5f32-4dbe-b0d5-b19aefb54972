"use client"

import { useCart } from "@/context/cart-context"
import MealBoxSelector from "./meal-box-selector"

export default function MealBoxSelectorWrapper() {
  const { showBoxSelector, setShowBoxSelector, setBoxSize, pendingItem, addItemDirectly, setPendingItem, boxSize } =
    useCart()

  const handleClose = () => {
    setShowBoxSelector(false)
    setPendingItem(null)
  }

  const handleSelect = (size: number) => {
    // First set the box size
    setBoxSize(size)

    // Then close the selector
    setShowBoxSelector(false)

    // If there's a pending item, add it to the cart
    if (pendingItem) {
      // Store the pending item locally before clearing it
      const itemToAdd = { ...pendingItem }

      // Clear the pending item immediately to prevent re-triggering
      setPendingItem(null)

      // Use requestAnimationFrame to ensure state updates have propagated
      requestAnimationFrame(() => {
        // Directly add the item without going through the normal addItem flow
        // that would check for box size again
        addItemDirectly(itemToAdd)
      })
    }
  }

  return <MealBoxSelector isOpen={showBoxSelector} onClose={handleClose} onSelect={handleSelect} />
}
