import type { Metadata } from "next"
import { getPageSeo } from "@/services/seo-service"

export async function generateMetadata(): Promise<Metadata> {
  const pageSeo = await getPageSeo("menu")

  return {
    title: pageSeo?.seo?.pagetitle || "Our Menu - EZeats Canada",
    description: pageSeo?.seo?.desc || "Browse our delicious menu of fresh meals and tiffin services.",
    keywords: pageSeo?.seo?.keywords || "meal menu, tiffin service, food delivery menu",
  }
}
