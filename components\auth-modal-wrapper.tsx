"use client"

import { useState, useEffect } from "react"
import LoginModal from "./login-modal"
import SignupModal from "./signup-modal"
import ForgotPasswordModal from "./forgot-password-modal"

// Update the type to include the forgot password view
type AuthModalWrapperProps = {
  isOpen: boolean
  initialView?: "login" | "signup" | "forgot-password"
  onClose: () => void
}

export default function AuthModalWrapper({ isOpen, initialView = "login", onClose }: AuthModalWrapperProps) {
  const [currentView, setCurrentView] = useState<"login" | "signup" | "forgot-password">(initialView)

  // Update currentView when initialView changes
  useEffect(() => {
    if (isOpen) {
      setCurrentView(initialView)
    }
  }, [initialView, isOpen])

  if (!isOpen) return null

  return (
    <>
      {currentView === "login" ? (
        <LoginModal
          isOpen={isOpen}
          onClose={onClose}
          onSwitchToSignup={() => setCurrentView("signup")}
          onSwitchToForgotPassword={() => setCurrentView("forgot-password")}
        />
      ) : currentView === "signup" ? (
        <SignupModal isOpen={isOpen} onClose={onClose} onSwitchToLogin={() => setCurrentView("login")} />
      ) : (
        <ForgotPasswordModal isOpen={isOpen} onClose={onClose} onBackToLogin={() => setCurrentView("login")} />
      )}
    </>
  )
}
