"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { X } from "lucide-react"
import { useToast } from "@/context/toast-context"
import { BUSINESS_ID } from "@/constants/app-constants"

// Update the props type to simplify
type ForgotPasswordModalProps = {
  isOpen: boolean
  onClose: () => void
  onBackToLogin: () => void
}

export default function ForgotPasswordModal({ isOpen, onClose, onBackToLogin }: ForgotPasswordModalProps) {
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const modalRef = useRef<HTMLDivElement>(null)
  const { showToast } = useToast()

  // Handle click outside to close modal
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [isOpen, onClose])

  // Handle ESC key to close modal
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        onClose()
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey)
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey)
    }
  }, [isOpen, onClose])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)

    try {
      const response = await fetch(
        `https://td0c8x9qb3.execute-api.us-east-1.amazonaws.com/prod/v1/business/${BUSINESS_ID}/user/forgot-password`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            email,
            domain: "ezeats.ca",
          }),
        },
      )

      const data = await response.json()

      if (response.ok && data.status === 200) {
        // Show success message
        showToast({
          message: "Password reset link sent successfully to your email",
          type: "success",
          duration: 5000,
        })

        // Close the modal
        onClose()
      } else {
        // Show error message
        showToast({
          message: data.message || "Failed to send password reset link. Please try again.",
          type: "error",
          duration: 4000,
        })
      }
    } catch (error) {
      console.error("Forgot password error:", error)
      showToast({
        message: "An error occurred. Please try again later.",
        type: "error",
        duration: 4000,
      })
    } finally {
      setIsLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <div
        ref={modalRef}
        className="bg-white rounded-lg w-full max-w-md overflow-hidden font-poppins"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-anton uppercase tracking-wide">Forgot password</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={24} />
            </button>
          </div>

          <form onSubmit={handleSubmit}>
            <div className="mb-6">
              <label htmlFor="email" className="block text-sm font-medium text-gray-600 mb-1">
                Email address
              </label>
              <input
                type="email"
                id="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full p-3 bg-gray-100 border border-gray-200 rounded-md font-poppins"
                placeholder="Enter your email"
                required
              />
            </div>

            <div className="flex justify-end mb-4">
              <button type="button" onClick={onBackToLogin} className="text-sm text-gray-600 hover:underline">
                Back to Login
              </button>
            </div>

            <button
              type="submit"
              disabled={isLoading}
              className="w-full py-3 bg-black text-white rounded-md font-medium hover:bg-gray-900 transition-colors disabled:opacity-70 disabled:cursor-not-allowed"
            >
              {isLoading ? "Submitting..." : "Submit"}
            </button>
          </form>
        </div>
      </div>
    </div>
  )
}
