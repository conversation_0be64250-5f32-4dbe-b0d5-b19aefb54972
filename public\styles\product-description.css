/* Reset Tailwind's influence for the product modal */
.detail_arrow {
  font-size: 20px;
  transition: transform 0.3s ease;
}
.newcollapse {
  display: none;
}
.newcollapse.newshow {
  display: block;
}

/* Rotate the arrow when the accordion is open */
.detail_arrow.rotate {
  transform: rotate(180deg);
}
/* .product-modal-content {
  all: revert;
  font-family: inherit;
  box-sizing: border-box;
}

.product-modal-content * {
  font-family: inherit;
  box-sizing: border-box;
}
*/
/* This creates a scope that resets Tailwind's influence */

.pro_model_scroll {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.pro_model_scroll::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
/* Reset all properties that might be affected by Tailwind */

.product-modal-content {
  all: revert;
  font-family: inherit;
  box-sizing: border-box;
}

/* Override specific Tailwind classes that might affect our modal */
.product-modal-content * {
  font-family: inherit;
  box-sizing: border-box;
}

.product-modal-content h1,
.product-modal-content h2,
.product-modal-content h3,
.product-modal-content h4,
.product-modal-content h5,
.product-modal-content h6,
.product-modal-content p,
.product-modal-content span,
.product-modal-content div {
  /* Remove the 'all: revert' which is too aggressive */
  margin: initial;
  padding: initial;
  /* Keep these properties configurable by your custom CSS */
  /* font-size: initial;
      font-weight: initial;
      line-height: initial;
      color: initial;
      text-align: initial; */
}

/* Critical styles for the modal that must override Tailwind */
.product-modal-content .log_des_acc_header span.title {
  letter-spacing: 0em !important;
  text-transform: uppercase !important;
  font-family: Poppins, sans-serif !important;
  font-size: 36px !important;
  font-weight: 500 !important;
  line-height: 54px !important;
  text-align: left !important;
  color: #585858 !important;
  padding-right: 15px !important;
}

.product-modal-content .log_des_acc_header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  width: 100% !important;
  text-align: left !important;
  padding: 15px 0 !important;
  border-bottom: 1px solid #e5e5e5 !important;
  background-color: transparent !important;
  cursor: pointer !important;
}

.product-modal-content .log_des_acc_header .acc_icon {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 24px !important;
  min-height: 24px !important;
}

.product-modal-content .log_des_acc_header .acc_icon i,
.product-modal-content .log_des_acc_header .acc_icon em {
  font-size: 16px !important;
  color: #686868 !important;
  display: inline-block !important;
  width: auto !important;
  height: auto !important;
  visibility: visible !important;
  opacity: 1 !important;
}

.product-modal-content .fas {
  font-family: "Font Awesome 5 Free" !important;
  font-weight: 900 !important;
  -moz-osx-font-smoothing: grayscale !important;
  -webkit-font-smoothing: antialiased !important;
  display: inline-block !important;
  font-style: normal !important;
  font-variant: normal !important;
  text-rendering: auto !important;
  line-height: 1 !important;
}

.product-modal-content .fa-angle-down:before {
  content: "\\f107" !important;
}

.product-modal-content .log_des_acc_header .rotate-icon {
  transition: transform 0.3s ease !important;
}

.product-modal-content .log_des_acc_header:not(.collapsed) .rotate-icon {
  transform: rotate(180deg) !important;
  font-size: 18px !important;
  font-weight: 200 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

.product-modal-content .log_des_toggle_data {
  display: grid !important;
  gap: 20px !important;
  width: 100% !important;
}

.product-modal-content .log_des_toggle_data p {
  font-family: Poppins, sans-serif !important;
  font-size: 20px !important;
  font-weight: 400 !important;
  line-height: 30px !important;
  text-align: left !important;
  color: #767676 !important;
}
.product_detail_page_headings h2 {
  text-transform: uppercase;
  color: #000000;
  margin-bottom: 0px !important;
  padding-right: 10px;
  padding-bottom: 0px;
  font-family: Poppins;
  font-size: 46px;
  line-height: 75px;
  letter-spacing: 0.22em;
  text-align: left;
  font-weight: 500 !important;
}
.pop_header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.pop_header h6 {
  letter-spacing: 0em;
  color: #000000;
  font-family: Poppins;
  font-size: 24px;
  font-weight: 500;
  line-height: 42px;
  text-align: left;
  margin: 0px;
}
.pop_box_1 {
  align-items: center;
}
.pop_box_1 img {
  border-radius: 40px;
}
@media (max-width: 1024px) {
  .product_detail_page_headings h2 {
    font-size: 20px;
    line-height: 34px;
  }
}
@media (max-width: 540px) {
  .product-modal-content .log_des_acc_header span.title {
    font-size: 18px !important;
    line-height: 24px !important;
  }
}

/* Add a custom chevron using CSS instead, but only for collapse headers */
.product-modal-content .log_des_acc_header .acc_icon {
  position: relative !important;
  width: 20px !important;
  height: 20px !important;
}

.product-modal-content .log_des_acc_header .acc_icon:before,
.product-modal-content .log_des_acc_header .acc_icon:after {
  content: "" !important;
  position: absolute !important;
  background-color: #686868 !important;
  transition: transform 0.3s ease !important;
}

.product-modal-content .log_des_acc_header .acc_icon:before {
  width: 2px !important;
  height: 10px !important;
  top: 5px !important;
  left: 9px !important;
  opacity: 0 !important;
}

.product-modal-content .log_des_acc_header .acc_icon:after {
  width: 10px !important;
  height: 2px !important;
  top: 9px !important;
  left: 5px !important;
}

.product-modal-content .log_des_acc_header.collapsed .acc_icon:before {
  opacity: 1 !important;
}

.add_review_btn {
  font-size: 15px;
  font-weight: 600;
  line-height: 23px;
  letter-spacing: 0em;
  background-color: #000000;
  color: #ffffff !important;
  border: 1px solid #000000;
  width: 176px;
  height: 42px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50px;
  cursor: pointer;
}

@media (max-width: 540px) {
  .add_review_btn {
    font-size: 12px;
    width: 126px;
    height: 34px;
  }
}

.overall-rating-heading {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  text-transform: capitalize;
  margin-bottom: 8px;
}

.main_rating {
  font-family: Poppins;
  font-size: 64px;
  font-weight: 400;
  line-height: 96px;
  letter-spacing: 0em;
  padding-right: 18px;
  margin: 0px;
}

@media (max-width: 540px) {
  .main_rating {
    font-size: 44px;
    line-height: 70px;
    padding-right: 15px;
  }
}
.reviews-heading {
  padding-bottom: 20px;
  margin: 0px;
  letter-spacing: 0em;
  text-transform: uppercase;
  font-family: Poppins;
  font-size: 36px;
  font-weight: 500;
  line-height: 54px;
  text-align: left;
  color: #585858;
  padding-right: 15px;
}

@media (max-width: 540px) {
  .reviews-heading {
    font-size: 18px;
    line-height: 24px;
    padding-bottom: 15px;
  }
}

.rating-overview-heading {
  font-size: 24px;
}

@media (max-width: 540px) {
  .rating-overview-heading {
    font-size: 20px;
  }
}

/* Original product description styles */
.log_des_data_parent {
  display: inline-block !important;
  width: 100% !important;
  padding: 0px 0px !important;
}

.custom_container {
  max-width: 1140px !important;
  margin: 0 auto !important;
}
.log_des_acc_main {
  display: block !important;
  width: 100% !important;
  margin-bottom: 30px !important;
}

.log_des_acc_header {
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  padding-bottom: 30px !important;
  z-index: 999 !important;
  border-bottom: none !important;
}
.log_des_acc_header:is(.collapsed) {
  padding-bottom: 0px !important;
}
.log_des_acc_header span.title {
  letter-spacing: 0em !important;
  text-transform: uppercase !important;
  font-family: Poppins, sans-serif !important;
  font-size: 36px !important;
  font-weight: 500 !important;
  line-height: 54px !important;
  text-align: left !important;
  color: #585858 !important;
  padding-right: 15px !important;
}

/* Add a custom chevron using CSS instead, but only for collapse headers */
.log_des_acc_header .acc_icon {
  position: relative !important;
  width: 20px !important;
  height: 20px !important;
}

.log_des_acc_header .acc_icon:before,
.log_des_acc_header .acc_icon:after {
  content: "" !important;
  position: absolute !important;
  background-color: #686868 !important;
  transition: transform 0.3s ease !important;
}

.log_des_acc_header .acc_icon:before {
  width: 2px !important;
  height: 10px !important;
  top: 5px !important;
  left: 9px !important;
  opacity: 0 !important;
}

.log_des_acc_header .acc_icon:after {
  width: 10px !important;
  height: 2px !important;
  top: 9px !important;
  left: 5px !important;
}

.log_des_acc_header.collapsed .acc_icon:before {
  opacity: 1 !important;
}

.log_des_acc_header .acc_icon i {
  font-size: 16px !important;
}

.log_des_acc_header .rotate-icon {
  transition: transform 0.3s ease !important;
}

.log_des_acc_header:not(.collapsed) .rotate-icon {
  transform: rotate(180deg) !important;
  font-size: 30px !important;
  font-weight: 200 !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}
em.fas.fa-angle-down.rotate-icon {
  color: #686868 !important;
  font-size: 18px !important;
}
.log_des_toggle_data {
  transition: all 0.3s ease !important;
  display: grid !important;
  gap: 20px !important;
  width: 100% !important;
}
.log_des_toggle_data.hidden {
  display: none !important;
}

.log_des_toggle_data.block {
  display: block !important;
}

.log_des_toggle_data p {
  font-family: Poppins, sans-serif !important;
  font-size: 20px !important;
  font-weight: 400 !important;
  line-height: 30px !important;
  text-align: left !important;
  color: #767676 !important;
  margin-bottom: 1em !important;
}
.log_des_toggle_data figure {
  display: grid !important;
  grid-template-columns: 1fr 10fr !important;
  align-items: center !important;
  gap: 17px !important;
}
.log_des_toggle_data figure img {
  object-fit: cover !important;
  width: 100% !important;
}
ul.log_des_toggle_data_listing {
  display: grid !important;
  width: 100% !important;
  grid-template-columns: 1fr 1fr 1fr !important;
  gap: 24px 24px !important;
  padding: 0px !important;
  margin: 0px !important;
}
ul.log_des_toggle_data_listing li {
  font-family: Poppins, sans-serif !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 18px !important;
  letter-spacing: 0em !important;
  color: #000 !important;
  list-style: none !important;
  display: flex !important;
  align-items: center !important;
}
ul.log_des_toggle_data_listing li i {
  color: #000 !important;
  font-size: 8px !important;
  padding-right: 10px !important;
}

ul.log_des_toggle_data_listing_two {
  display: grid !important;
  width: 100% !important;
  grid-template-columns: 1fr 1fr 1fr !important;
  gap: 24px 24px !important;
  padding: 0px !important;
  margin: 0px !important;
}
ul.log_des_toggle_data_listing_two li {
  font-family: Poppins, sans-serif !important;
  font-size: 14px !important;
  font-weight: 400 !important;
  line-height: 18px !important;
  letter-spacing: 0em !important;
  color: #000 !important;
  list-style: none !important;
  display: flex !important;
  align-items: center !important;
}
ul.log_des_toggle_data_listing_two li span {
  font-family: Poppins, sans-serif !important;
  font-size: 14px !important;
  font-weight: 700 !important;
  line-height: 18px !important;
  letter-spacing: 0em !important;
  margin-right: 10px !important;
  width: 30px !important;
  height: 30px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  border-radius: 50% !important;
  background-color: #000 !important;
  color: #fff !important;
  min-width: 30px !important;
}
.log_des_rating_box {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.log_des_rating_num {
  display: flex;
  flex-direction: column;
  width: 100%;
  padding-bottom: 15px;
  margin-bottom: 32px;
}
.log_des_rating_num h3 {
  font-family: Anton;
  font-size: 24px;
  font-weight: 400;
  line-height: 36px;
  letter-spacing: 0em;
  text-transform: uppercase;
  color: #000;
  padding-bottom: 20px;
  margin: 0px;
}
.log_des_rating_num small {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  text-transform: capitalize;
}
.log_des_rating_num_detail {
  display: flex;
  align-items: center;
  width: 100%;
}
.log_des_rating_num_detail h2 {
  font-family: Poppins;
  font-size: 64px;
  font-weight: 600;
  line-height: 96px;
  letter-spacing: 0em;
  padding-right: 18px;
  margin: 0px;
}
.log_des_total_rating {
  display: flex;
  flex-direction: column;
}
.log_des_total_rating_stars {
  display: flex;
}
.log_des_total_rating_stars i {
  padding-right: 4px;
  font-size: 20px;
  color: #ffa800;
}
.log_des_total_rating p {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 18px;
  letter-spacing: 0em;
  margin: 0px;
}
.log_des_review_list_main {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.log_des_review_list_main h3 {
  padding-bottom: 20px;
  margin: 0px;
  letter-spacing: 0em;
  text-transform: uppercase;
  font-family: Poppins;
  font-size: 36px;
  font-weight: 500;
  line-height: 54px;
  text-align: left;
  color: #585858;
  padding-right: 15px;
}
.log_des_review_list {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.log_des_single_review {
  border-bottom: 1px solid #00000026;
  padding-bottom: 24px;
  margin-bottom: 24px;
  display: grid;
  width: 100%;
  grid-template-columns: 1fr 3fr;
  gap: 20px 30px;
}
.log_des_single_review figure {
  display: flex;
  align-items: flex-start;
  margin: 0px;
}
.log_des_single_review figure img {
  margin-right: 10px;
  width: 48px;
  height: 48px;
  object-fit: cover;
}
.log_des_single_review figure figcaption {
  display: flex;
  flex-direction: column;
}
.log_des_single_review figure figcaption h5 {
  font-family: Poppins;
  font-size: 15px;
  font-weight: 600;
  line-height: 23px;
  letter-spacing: 0em;
  color: #212121;
  margin: 0px;
}
.log_des_single_review figure figcaption span {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  letter-spacing: 0em;
  color: #9fa19d;
}
.log_des_single_review_data {
  display: flex;
  flex-direction: column;
  width: 100%;
}
.log_des_single_review_stars {
  display: flex;
  padding-bottom: 8px;
}
.log_des_single_review_stars i {
  padding-right: 4px;
  font-size: 16px;
  color: #ffa800;
}
.log_des_single_review_data p {
  font-family: Poppins;
  font-size: 14px;
  font-weight: 400;
  line-height: 22px;
  letter-spacing: 0em;
  color: #464646;
  margin: 0px;
}
.detail_page_long_des_table {
  display: grid;
  grid-template-columns: 1fr;
  width: 100%;
  max-width: 900px;
  margin: 0 auto;
  border: 1px solid #6e6e6e;
  border-radius: 52px;
}
.detail_page_long_des_table_heading_main h2 {
  font-family: Poppins;
  font-size: 52px;
  font-weight: 500 !important;
  line-height: 77.99px;
  letter-spacing: 0.35em;
  text-align: center;
  color: #585858;
  margin: 0px;
  border-bottom: 1px solid #a3a3a3;
}
.detail_page_long_des_table_heading {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 0px 50px;
}

.detail_page_long_des_table_heading h2 {
  background: #ffffff;
  font-style: normal;
  color: #767676;
  padding: 15px 5px;
  margin: 0px;
  word-break: break-all;
  height: 100%;
  align-items: flex-end;
  display: flex;
  font-family: Poppins;
  font-size: 20px;
  font-weight: 500 !important;
  line-height: 23.4px;
  text-align: right;
  justify-content: flex-end;
  flex-direction: column;
}
.detail_page_long_des_table_heading.long_des_heading_2 {
  grid-template-columns: 1fr 1fr;
}

.detail_page_long_des_table_detail.long_des_table_detail_2 {
  grid-template-columns: 1fr 1fr;
}

.detail_page_long_des_table_detail {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 0px 50px;
  border-bottom: 1px solid #a3a3a3;
}
.detail_page_long_des_table_detail:last-child {
  border: unset;
}
.detail_page_long_des_table_detail span {
  color: #767676;
  margin: 0px;
  padding: 12px 5px;
  height: 100%;
  align-items: center;
  display: flex;
  word-break: break-all;
  font-family: Poppins;
  font-size: 20px;
  font-weight: 400;
  line-height: 44px;
  text-align: right;
  justify-content: flex-end;
}
.detail_page_long_des_table_detail span:first-child {
  justify-content: flex-start;
  text-align: left;
}

@media (max-width: 540px) {
  .log_des_acc_header span.title {
    font-size: 18px !important;
    line-height: 24px !important;
  }
  .log_des_acc_main {
    margin-bottom: 20px !important;
  }
  ul.log_des_toggle_data_listing {
    grid-template-columns: 1fr 1fr !important;
    gap: 20px 20px !important;
  }
  ul.log_des_toggle_data_listing_two {
    grid-template-columns: 1fr !important;
    gap: 20px 20px !important;
  }
  ul.log_des_toggle_data_listing_two li span {
    width: 25px !important;
    height: 25px !important;
    min-width: 25px !important;
  }
  .log_des_rating_num h3 {
    font-size: 18px !important;
    line-height: 24px !important;
    padding-bottom: 15px !important;
  }
  .log_des_rating_num_detail h2 {
    font-size: 44px !important;
    line-height: 70px !important;
    padding-right: 15px !important;
  }
  .log_des_total_rating_stars i {
    font-size: 16px !important;
  }
  .log_des_total_rating p {
    font-size: 12px !important;
  }

  .log_des_rating_num {
    padding-bottom: 10px !important;
    margin-bottom: 20px !important;
  }
  .log_des_review_list_main h3 {
    font-size: 18px !important;
    line-height: 24px !important;
    padding-bottom: 15px !important;
  }
}
@media (max-width: 768px) {
  .log_des_single_review {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 1024px) {
  .product_detail_page_heading h2 {
    font-size: 20px;
    line-height: 34px;
  }
  .product_detail_page_add_to_cart_btn {
    padding-left: 10px;
  }

  .detail_page_long_des_table_heading_main h2 {
    font-size: 24px;
    line-height: 54.99px;
  }
  .detail_page_long_des_table_heading h2 {
    font-size: 16px;
  }
  .detail_page_long_des_table_heading {
    padding: 0px 15px;
  }
  .detail_page_long_des_table_detail {
    padding: 0px 15px;
  }
  .detail_page_long_des_table_detail span {
    font-size: 16px;
  }
  .log_des_toggle_data p {
    font-size: 16px;
    line-height: 24px;
  }
  .log_des_toggle_data figure {
    grid-template-columns: 1fr 12fr;
  }
  .log_des_acc_header span.title {
    font-size: 26px !important;
    line-height: 36px !important;
  }
}
@media (max-width: 540px) {
  .detail_page_long_des_table_detail {
    padding: 0px 10px;
  }
  .detail_page_long_des_table_heading {
    padding: 0px 10px;
  }
  .detail_page_long_des_table_heading_main h2 {
    font-size: 16px;
    line-height: 40.99px;
  }
  .detail_page_long_des_table_heading h2 {
    font-size: 12px;
    line-height: 17px;
  }
  .detail_page_long_des_table_detail span {
    font-size: 12px;
    line-height: 26px;
  }
  .detail_page_long_des_table {
    border-radius: 24px;
  }
  .log_des_data_parent {
    padding: 0px 0px;
  }
  .log_des_toggle_data p {
    font-size: 14px;
    line-height: 21px;
  }
  .log_des_acc_header span.title {
    font-size: 18px !important;
    line-height: 29px !important;
  }
  .log_des_toggle_data {
    gap: 12px;
  }
}
@media (max-width: 370px) {
  .detail_page_long_des_table_heading h2 {
    font-size: 10px;
    line-height: 17px;
  }
  .detail_page_long_des_table_detail span {
    font-size: 10px;
    line-height: 17px;
  }
}

.fixed_btn {
  position: fixed;
  bottom: 20px;
  z-index: 999;
  right: 20px;
}
.fixed_btn a {
  font-family: Poppins;
  font-size: 23px;
  font-weight: 400;
  line-height: 23px;
  letter-spacing: 0em;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 208px;
  height: 52px;
  border-radius: 2px;
  text-transform: capitalize;
  position: relative;
  cursor: pointer;
  box-shadow: rgba(17, 12, 46, 0.15) 0px 48px 100px 0px;
}
@media (max-width: 1024px) {
  .fixed_btn a {
    font-size: 18px;
    width: 155px;
    height: 42px;
  }
}
@media (max-width: 768px) {
  .fixed_btn a {
    height: 43px;
    width: 148px;
  }
}
@media (max-width: 540px) {
  .fixed_btn a {
    width: 132px;
    height: 40px;
    font-size: 16px;
  }
}

.sub_desc_toggle_hidden {
  display: none;
}

.sub_desc_head {
  text-decoration: underline;
  cursor: pointer;
}

.custom_toggle_btn {
  display: flex;
  align-items: center;
  width: 100%;
  justify-content: space-between;
}
.custom_toggle_btn p {
  margin: 0px !important;
}

.cust_toggle_btn_child.log_des_toggle_data {
  width: unset !important;
}
