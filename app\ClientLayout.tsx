"use client";

import type React from "react";
import Header from "@/components/header";
import Footer from "@/components/footer";
import { CartProvider } from "@/context/cart-context";
import { ToastProvider } from "@/context/toast-context";
import { BusinessProvider } from "@/context/business-context";
import {
  ProductModalProvider,
  useProductModal,
} from "@/context/product-modal-context";
import MealBoxSelectorWrapper from "@/components/meal-box-selector-wrapper";
import Cart from "@/components/cart";
import ProductDetailModal from "@/components/product-detail-modal";
import { usePathname } from "next/navigation";
import ScheduleModalWrapper from "@/components/schedule-modal-wrapper";
// Import the AuthProvider
import { AuthProvider } from "@/context/auth-context";
// Import the ErrorBoundary
import { ErrorBoundary } from "@/components/error-boundary";
// Import the SeoProvider
import { SeoProvider } from "@/context/seo-context";
// Import analytics utilities
import { trackPageView, initializeAnalytics } from "@/utils/analytics";
import { useEffect } from "react";

// Wrapper component to access the context
function ProductDetailModalWrapper() {
  const { isModalOpen, selectedProductId, closeProductModal } =
    useProductModal();
  return (
    <ProductDetailModal
      isOpen={isModalOpen}
      onClose={closeProductModal}
      productId={selectedProductId}
    />
  );
}

// Wrap the entire content in the ErrorBoundary
export default function ClientLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const pathname = usePathname() || "";
  const isCheckoutPage = pathname === "/checkout";
  const isProfilePage = pathname.startsWith("/profile");
  const isHomePage = pathname === "/";

  // Initialize analytics once when component mounts
  useEffect(() => {
    initializeAnalytics();
  }, []);

  // Track page views when pathname changes
  useEffect(() => {
    // Get page title from document if available, or use a default based on pathname
    const pageTitle =
      document.title || `${pathname.split("/").pop() || "Home"}`;

    // Track the page view
    trackPageView(pathname, pageTitle);
  }, [pathname]);

  return (
    <ErrorBoundary>
      <SeoProvider>
        <ToastProvider>
          <BusinessProvider>
            <AuthProvider>
              <CartProvider>
                <ProductModalProvider>
                  <ErrorBoundary
                    fallback={
                      <div className="p-4 bg-red-50">Error loading header</div>
                    }
                  >
                    <Header />
                  </ErrorBoundary>

                  <ErrorBoundary
                    fallback={
                      <div className="p-4 bg-red-50">
                        Error loading main content
                      </div>
                    }
                  >
                    {children}
                  </ErrorBoundary>

                  {/* Always render Footer but conditionally display it */}
                  <div
                    className="footer_parent_box"
                    style={{
                      display:
                        isCheckoutPage || isProfilePage ? "none" : "block",
                    }}
                  >
                    <ErrorBoundary
                      fallback={
                        <footer className="bg-black text-white py-4">
                          <div className="container mx-auto px-4">
                            <p className="text-center">
                              © {new Date().getFullYear()} EZeats. All rights
                              reserved.
                            </p>
                          </div>
                        </footer>
                      }
                    >
                      <Footer />
                    </ErrorBoundary>
                  </div>

                  <ErrorBoundary fallback={null}>
                    <MealBoxSelectorWrapper />
                  </ErrorBoundary>

                  <ErrorBoundary fallback={null}>
                    <Cart />
                  </ErrorBoundary>

                  {isHomePage && (
                    <ErrorBoundary fallback={null}>
                      <ScheduleModalWrapper preventAutoOpen={true} />
                    </ErrorBoundary>
                  )}

                  <ErrorBoundary fallback={null}>
                    <ProductDetailModalWrapper />
                  </ErrorBoundary>
                </ProductModalProvider>
              </CartProvider>
            </AuthProvider>
          </BusinessProvider>
        </ToastProvider>
      </SeoProvider>
    </ErrorBoundary>
  );
}
